#!/bin/bash

# 视频分析服务停止脚本
# 作者: Augment Agent
# 用途: 停止ASR服务和视频分析服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_DIR="/Users/<USER>/Documents/work/augment-projects/material-review"
LOG_DIR="$PROJECT_DIR/logs"

echo -e "${BLUE}🛑 视频分析服务停止脚本${NC}"
echo -e "${BLUE}================================${NC}"

# 停止服务函数
stop_service() {
    local port=$1
    local service_name=$2
    local pid_file="$3"
    
    echo -e "${YELLOW}停止 $service_name (端口$port)...${NC}"
    
    # 方法1: 通过PID文件停止
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            kill $pid
            sleep 2
            if ps -p $pid > /dev/null 2>&1; then
                kill -9 $pid
            fi
            echo -e "${GREEN}✅ 通过PID停止 $service_name (PID: $pid)${NC}"
        fi
        rm -f "$pid_file"
    fi
    
    # 方法2: 通过端口停止
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}   通过端口停止 $service_name...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null
        sleep 1
    fi
    
    # 验证是否停止
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}❌ $service_name 停止失败${NC}"
        return 1
    else
        echo -e "${GREEN}✅ $service_name 已停止${NC}"
        return 0
    fi
}

# 停止所有Python进程（可选）
stop_all_python_services() {
    echo -e "${YELLOW}🔍 查找相关Python进程...${NC}"
    
    # 查找ASR相关进程
    local asr_pids=$(ps aux | grep "app.server" | grep -v grep | awk '{print $2}')
    if [ ! -z "$asr_pids" ]; then
        echo -e "${YELLOW}   停止ASR相关进程: $asr_pids${NC}"
        echo $asr_pids | xargs kill -9 2>/dev/null
    fi
    
    # 查找视频分析相关进程
    local video_pids=$(ps aux | grep "main_server" | grep -v grep | awk '{print $2}')
    if [ ! -z "$video_pids" ]; then
        echo -e "${YELLOW}   停止视频分析相关进程: $video_pids${NC}"
        echo $video_pids | xargs kill -9 2>/dev/null
    fi
}

# 清理日志（可选）
cleanup_logs() {
    if [ "$1" = "--clean-logs" ]; then
        echo -e "${YELLOW}🧹 清理日志文件...${NC}"
        rm -f "$LOG_DIR/asr_service.log"
        rm -f "$LOG_DIR/video_service.log"
        echo -e "${GREEN}✅ 日志文件已清理${NC}"
    fi
}

# 显示当前状态
show_status() {
    echo -e "\n${BLUE}📊 当前状态${NC}"
    echo -e "${BLUE}================================${NC}"
    
    # 检查ASR服务
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}🎤 ASR服务 (8080): 仍在运行${NC}"
    else
        echo -e "${GREEN}🎤 ASR服务 (8080): 已停止${NC}"
    fi
    
    # 检查视频分析服务
    if lsof -Pi :8081 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}🎬 视频分析服务 (8081): 仍在运行${NC}"
    else
        echo -e "${GREEN}🎬 视频分析服务 (8081): 已停止${NC}"
    fi
    
    # 显示相关进程
    local python_processes=$(ps aux | grep -E "(app.server|main_server)" | grep -v grep)
    if [ ! -z "$python_processes" ]; then
        echo -e "\n${YELLOW}⚠️  仍有相关Python进程运行:${NC}"
        echo "$python_processes"
    else
        echo -e "\n${GREEN}✅ 没有相关Python进程运行${NC}"
    fi
}

# 主函数
main() {
    echo -e "${YELLOW}开始停止服务...${NC}\n"
    
    # 停止ASR服务
    stop_service 8080 "ASR服务" "$LOG_DIR/asr.pid"
    
    # 停止视频分析服务
    stop_service 8081 "视频分析服务" "$LOG_DIR/video.pid"
    
    # 停止所有相关Python进程
    stop_all_python_services
    
    # 清理日志（如果指定）
    cleanup_logs "$1"
    
    # 显示状态
    show_status
    
    echo -e "\n${GREEN}🎉 服务停止完成！${NC}"
    echo -e "${BLUE}重新启动服务: ./start_services.sh${NC}"
}

# 显示帮助
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo -e "${BLUE}用法:${NC}"
    echo -e "  ./stop_services.sh              # 停止所有服务"
    echo -e "  ./stop_services.sh --clean-logs # 停止服务并清理日志"
    echo -e "  ./stop_services.sh --help       # 显示帮助"
    exit 0
fi

# 运行主函数
main "$1"
