# OCR文字识别API接口文档

## 📋 服务概述

**服务名称**: RapidOCR文字识别服务  
**模型版本**: RapidOCR 1.3.7  
**推理引擎**: ONNX Runtime  
**服务地址**: http://************:8081/ocr

## 🔧 技术特性

- ✅ **多语言支持**: 中文、英文等多语言文字识别
- ✅ **高精度识别**: 基于深度学习的OCR模型
- ✅ **位置检测**: 提供文字在图片中的精确位置
- ✅ **置信度评估**: 每个识别结果都包含置信度分数
- ✅ **高性能推理**: ONNX Runtime优化推理速度

## 📚 API接口列表

### 1. OCR文字识别接口

**接口地址**: `POST /ocr`  
**功能描述**: 对上传的图片进行文字识别

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 图片文件 |

#### 支持的文件格式
- JPG/JPEG
- PNG  
- BMP
- GIF

#### 请求示例

```bash
# 使用curl上传图片进行OCR识别
curl -X POST "http://************:8081/ocr" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/image.jpg"
```

#### 响应格式

```json
{
  "success": true,
  "trace_id": "trace_1754307123456_-1234567890",
  "filename": "test.jpg",
  "file_size": 28114,
  "image_info": {
    "width": 356,
    "height": 258,
    "format": "image/jpeg"
  },
  "ocr_result": {
    "texts": [
      "e生2023优享版",
      "续保",
      "订单时间：2025-04-09 20:25:30",
      "保费：45.5元"
    ],
    "details": [
      {
        "text": "e生2023优享版",
        "confidence": 0.95,
        "bbox": [[10, 20], [150, 20], [150, 45], [10, 45]],
        "position": {
          "x": 10,
          "y": 20,
          "width": 140,
          "height": 25
        }
      }
    ],
    "total_count": 4
  },
  "processing_time": "2025-08-05T18:52:03.123456"
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 识别是否成功 |
| trace_id | string | 请求追踪ID |
| filename | string | 原始文件名 |
| file_size | integer | 文件大小(字节) |
| image_info | object | 图片信息 |
| image_info.width | integer | 图片宽度 |
| image_info.height | integer | 图片高度 |
| image_info.format | string | 图片格式 |
| ocr_result | object | OCR识别结果 |
| ocr_result.texts | array | 识别到的文字列表 |
| ocr_result.details | array | 详细识别结果 |
| ocr_result.total_count | integer | 识别到的文字总数 |
| processing_time | string | 处理时间 |

#### details字段详细说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| text | string | 识别到的文字内容 |
| confidence | float | 置信度(0-1) |
| bbox | array | 文字边界框坐标 |
| position | object | 文字位置信息 |
| position.x | integer | 左上角X坐标 |
| position.y | integer | 左上角Y坐标 |
| position.width | integer | 文字区域宽度 |
| position.height | integer | 文字区域高度 |

### 2. OCR服务信息接口

**接口地址**: `GET /ocr/info`  
**功能描述**: 获取OCR服务的详细信息

#### 请求示例

```bash
curl -X GET "http://localhost:8081/ocr/info"
```

#### 响应示例

```json
{
  "service_name": "RapidOCR文字识别服务",
  "model_name": "RapidOCR",
  "model_version": "1.3.7",
  "engine": "ONNX Runtime",
  "available": true,
  "supported_formats": ["jpg", "jpeg", "png", "bmp", "gif"],
  "features": [
    "多语言文字识别",
    "文字位置检测", 
    "置信度评估",
    "高性能推理"
  ],
  "confidence_threshold": 0.5,
  "api_version": "1.0"
}
```

## ⚠️ 错误处理

### 常见错误码

| HTTP状态码 | 错误说明 | 解决方案 |
|------------|----------|----------|
| 400 | 文件格式不支持 | 请上传JPG、PNG等图片格式 |
| 400 | 无法读取图片文件 | 检查文件是否损坏 |
| 503 | OCR服务不可用 | 检查服务状态，重启服务 |
| 500 | 内部服务器错误 | 查看服务日志，联系技术支持 |

### 错误响应格式

```json
{
  "detail": "仅支持图片文件"
}
```

## 🚀 使用示例

### Python示例

```python
import requests

# OCR文字识别
def ocr_recognition(image_path):
    url = "http://localhost:8081/ocr"
    
    with open(image_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(url, files=files)
    
    if response.status_code == 200:
        result = response.json()
        print(f"识别到 {result['ocr_result']['total_count']} 个文字:")
        for text in result['ocr_result']['texts']:
            print(f"- {text}")
        return result
    else:
        print(f"识别失败: {response.text}")
        return None

# 使用示例
result = ocr_recognition("test.jpg")
```

### JavaScript示例

```javascript
// OCR文字识别
async function ocrRecognition(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        const response = await fetch('http://localhost:8081/ocr', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log(`识别到 ${result.ocr_result.total_count} 个文字:`);
            result.ocr_result.texts.forEach(text => {
                console.log(`- ${text}`);
            });
            return result;
        } else {
            console.error('识别失败:', await response.text());
            return null;
        }
    } catch (error) {
        console.error('请求失败:', error);
        return null;
    }
}

// 使用示例
const fileInput = document.getElementById('file-input');
fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        const result = await ocrRecognition(file);
    }
});
```

## 📊 性能指标

- **识别精度**: >95% (清晰图片)
- **处理速度**: <2秒 (1MB图片)
- **并发支持**: 支持多用户同时访问
- **置信度阈值**: 0.5 (可过滤低质量识别结果)

## 🔗 相关链接

- **OCR测试页面**: http://localhost:8081/ocr/test (可视化测试OCR功能)
- **服务健康检查**: http://localhost:8081/health
- **API文档**: http://localhost:8081/docs
- **Demo页面**: http://localhost:8081/demo

## 🧪 快速测试

### 在线测试页面
访问 http://localhost:8081/ocr/test 可以通过可视化界面测试OCR功能：

1. 打开测试页面
2. 选择或拖拽图片文件
3. 查看实时识别结果
4. 查看详细的位置和置信度信息

### 命令行测试
```bash
# 测试OCR服务信息
curl -X GET "http://localhost:8081/ocr/info"

# 测试OCR识别功能
curl -X POST "http://localhost:8081/ocr" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your_image.jpg"
```

---

**版本**: v1.0
**更新时间**: 2025-08-05
**技术支持**: 素材审核智能体团队
