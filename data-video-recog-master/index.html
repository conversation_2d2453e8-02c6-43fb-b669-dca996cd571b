
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>素材审核智能体</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#4568DC", secondary: "#B06AB3" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      background-color: #F5F7FA;
      font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
      }
      .gradient-bg {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      }
      .gradient-text {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      }
      .upload-area {
      border: 2px dashed #CBD5E0;
      transition: all 0.3s ease;
      }
      .upload-area:hover, .upload-area.dragging {
      border-color: #4568DC;
      background-color: rgba(69, 104, 220, 0.05);
      }
      .progress-circle {
      transform: rotate(-90deg);
      }
      .progress-circle-bg {
      fill: none;
      stroke: #E2E8F0;
      stroke-width: 4;
      }
      .progress-circle-path {
      fill: none;
      stroke-width: 4;
      stroke-linecap: round;
      transition: stroke-dashoffset 0.5s ease;
      }
      .file-input {
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      position: absolute;
      z-index: -1;
      }
      .step-connector {
      height: 2px;
      background-color: #CBD5E0;
      flex-grow: 1;
      margin: 0 8px;
      }
      .step-connector.active {
      background: linear-gradient(90deg, #4568DC, #B06AB3);
      }
      .step-circle {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #E2E8F0;
      color: #718096;
      font-weight: 600;
      }
      .step-circle.active {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      color: white;
      }
      .step-circle.completed {
      background: linear-gradient(135deg, #4568DC, #B06AB3);
      color: white;
      }
    </style>
  </head>
  <body class="min-h-screen">
    <header
      class="gradient-bg text-white py-4 px-6 shadow-md fixed w-full top-0 z-10"
    >
      <div class="container mx-auto flex items-center justify-between">
        <div class="flex items-center">
          <span class="text-2xl font-['Pacifico']">logo</span>
          <span class="ml-4 text-xl font-medium">素材审核智能体</span>
        </div>
        <!-- <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <div
              class="w-8 h-8 flex items-center justify-center rounded-full bg-white/20"
            >
              <i class="ri-notification-3-line text-white"></i>
            </div>
            <div
              class="w-8 h-8 flex items-center justify-center rounded-full bg-white/20"
            >
              <i class="ri-settings-3-line text-white"></i>
            </div>
          </div> -->
        </div>
      </div>
    </header>
    <main class="container mx-auto pt-24 pb-12 px-6 max-w-5xl">
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">素材审核</h1>
        <p class="text-gray-600">
          上传您需要审核的素材文件，支持图片、视频和文档格式
        </p>
      </div>
      <!-- 上传区域 -->
      <div id="upload-container" class="mb-12">
        <div
          id="upload-area"
          class="upload-area rounded-2xl p-10 flex flex-col items-center justify-center text-center cursor-pointer bg-white shadow-sm"
        >
          <div
            class="w-20 h-20 flex items-center justify-center rounded-full bg-blue-50 mb-4"
          >
            <i class="ri-upload-cloud-2-line ri-3x gradient-text"></i>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">
            点击或拖拽文件到此处上传
          </h3>
          <p class="text-gray-500 mb-6 max-w-md">
            支持单个或批量上传，每个文件大小不超过500MB
          </p>
          <div class="flex items-center justify-center space-x-4 mb-6">
            <div class="flex flex-col items-center">
              <div
                class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mb-2"
              >
                <i class="ri-image-line ri-lg text-primary"></i>
              </div>
              <span class="text-sm text-gray-600">图片</span>
            </div>
            <div class="flex flex-col items-center">
              <div
                class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mb-2"
              >
                <i class="ri-video-line ri-lg text-primary"></i>
              </div>
              <span class="text-sm text-gray-600">视频</span>
            </div>
            <div class="flex flex-col items-center">
              <div
                class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mb-2"
              >
                <i class="ri-file-text-line ri-lg text-primary"></i>
              </div>
              <span class="text-sm text-gray-600">文档</span>
            </div>
          </div>
          <button
            id="select-file-btn"
            class="gradient-bg text-white px-6 py-2.5 rounded-button font-medium shadow-sm hover:shadow-md transition-all !rounded-button"
          >
            选择文件
          </button>
          <input
            type="file"
            id="file-input"
            class="file-input"
            multiple
            accept=".jpg,.jpeg,.png,.mp4,.txt,.docx"
          />
          <p class="text-xs text-gray-500 mt-4">
            支持格式：JPG、PNG、MP4、TXT、DOCX
          </p>
        </div>
      </div>
      <!-- 上传进度区域 -->
      <div id="upload-progress-container" class="mb-12 hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">上传进度</h2>
        <div id="upload-progress-list" class="space-y-4">
          <!-- 上传进度项会在这里动态添加 -->
        </div>
      </div>
      <!-- 审核流程 -->
      <div id="review-process-container" class="mb-12 hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">审核流程</h2>
        <div class="bg-white rounded-2xl p-6 shadow-sm">
          <div class="flex items-center mb-8">
            <div class="flex flex-col items-center">
              <div id="step-1-circle" class="step-circle active">1</div>
              <div class="mt-2 text-center">
                <p id="step-1-text" class="text-sm font-medium text-primary">
                  文件上传
                </p>
                <p id="step-1-time" class="text-xs text-gray-500 mt-1">
                  2025-07-04 14:30
                </p>
              </div>
            </div>
            <div id="connector-1" class="step-connector"></div>
            <div class="flex flex-col items-center">
              <div id="step-2-circle" class="step-circle">2</div>
              <div class="mt-2 text-center">
                <p id="step-2-text" class="text-sm font-medium text-gray-500">
                  素材解析
                </p>
                <p id="step-2-time" class="text-xs text-gray-500 mt-1">
                  等待中
                </p>
              </div>
            </div>
            <div id="connector-2" class="step-connector"></div>
            <div class="flex flex-col items-center">
              <div id="step-3-circle" class="step-circle">3</div>
              <div class="mt-2 text-center">
                <p id="step-3-text" class="text-sm font-medium text-gray-500">
                  AI审核
                </p>
                <p id="step-3-time" class="text-xs text-gray-500 mt-1">
                  等待中
                </p>
              </div>
            </div>
            <div id="connector-3" class="step-connector"></div>
            <div class="flex flex-col items-center">
              <div id="step-4-circle" class="step-circle">4</div>
              <div class="mt-2 text-center">
                <p id="step-4-text" class="text-sm font-medium text-gray-500">
                  审核完成
                </p>
                <p id="step-4-time" class="text-xs text-gray-500 mt-1">
                  等待中
                </p>
              </div>
            </div>
          </div>
          <div class="p-4 bg-blue-50 rounded-lg">
            <div class="flex items-start">
              <div
                class="w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 mt-0.5"
              >
                <i class="ri-information-line text-primary"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-700" id="status-message">
                  文件已上传成功，正在进行素材解析...
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- JSON数据展示 -->
      <div id="json-data-container" class="mb-12 hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">审核详情</h2>
        <div class="bg-white rounded-2xl p-6 shadow-sm">
          <!-- 步骤1: 素材解析 -->
          <!-- 步骤1: 素材解析 -->
          <div class="mb-6" id="step-1-details" style="display: none;">
            <div class="flex items-center mb-3">
              <div class="w-6 h-6 flex items-center justify-center rounded-full bg-green-100 mr-3">
                <i class="ri-check-line text-green-600 text-sm"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-800">用户上传了素材，开始使用模型解析素材</h3>
            </div>
            <div class="ml-9">
              <div class="mb-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">解析结果为：</span>
                  <div class="flex gap-2">
                    <button id="toggle-analysis-btn" class="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                      <i class="ri-eye-line mr-1"></i>展开查看
                    </button>
                    <button id="copy-analysis-json-btn" class="px-3 py-1 text-sm bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                      <i class="ri-file-copy-line mr-1"></i>复制JSON
                    </button>
                  </div>
                </div>
              </div>
              <div id="analysis-result-collapsed" class="bg-gray-50 rounded-lg p-3 border">
                <p class="text-sm text-gray-700">AI模型正在解析素材，请稍候...</p>
              </div>
              <div id="analysis-result-expanded" class="bg-gray-50 rounded-lg p-4 border hidden">
                <div class="space-y-3">
                  <!-- 解析详情将在这里动态显示 -->
                  <div class="text-sm text-gray-500 text-center py-4">
                    正在解析素材，请稍候...
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 步骤2: AI审核开始 -->
          <div class="mb-6" id="step-2-details" style="display: none;">
            <div class="flex items-center mb-3">
              <div class="w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 mr-3">
                <i class="ri-play-line text-blue-600 text-sm"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-800">已完成素材解析，开始AI审核素材</h3>
            </div>
            <div class="ml-9">
              <div class="bg-blue-50 rounded-lg p-3 border">
                <p class="text-sm text-blue-700">正在使用AI模型进行内容审核，检测违规内容...</p>
              </div>
            </div>
          </div>

          <!-- 步骤3: AI审核结果 -->
          <div class="mb-6" id="step-3-details" style="display: none;">
            <div class="flex items-center mb-3">
              <div class="w-6 h-6 flex items-center justify-center rounded-full bg-green-100 mr-3">
                <i class="ri-check-line text-green-600 text-sm"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-800">AI审核完成</h3>
            </div>
            <div class="ml-9">
              <div id="audit-result-summary" class="bg-green-50 rounded-lg p-3 border mb-3">
                <p class="text-sm text-green-700">审核结果：内容合规，未发现违规内容</p>
              </div>

            </div>
          </div>



          <div class="text-xs text-gray-500">
            <p>💡 提示：此处展示完整的审核流程，包括素材解析、AI审核等步骤的详细信息。</p>
          </div>
        </div>
      </div>

      <!-- 审核结果 -->
      <div id="review-result-container" class="hidden">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">审核结果</h2>
        <div class="space-y-4" id="review-result-list">
          <!-- 审核结果项会在这里动态添加 -->
        </div>
      </div>
      <!-- 无审核记录提示 -->
      <div
        id="no-records"
        class="hidden bg-white rounded-2xl p-10 text-center shadow-sm"
      >
        <div
          class="w-20 h-20 mx-auto flex items-center justify-center rounded-full bg-gray-100 mb-4"
        >
          <i class="ri-file-list-3-line ri-3x text-gray-400"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-2">暂无审核记录</h3>
        <p class="text-gray-500 mb-6">您还没有提交任何素材进行审核</p>
      </div>
    </main>
    <!-- 审核详情弹窗 -->
    <div
      id="review-detail-modal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
    >
      <div
        class="bg-white rounded-2xl max-w-3xl w-full max-h-[90vh] overflow-auto p-6 mx-4"
      >
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-gray-800">审核详情</h3>
          <button
            id="close-modal"
            class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 cursor-pointer"
          >
            <i class="ri-close-line ri-lg text-gray-500"></i>
          </button>
        </div>
        <div id="modal-content" class="space-y-6">
          <!-- 弹窗内容会在这里动态添加 -->
        </div>
        <div class="mt-8 flex justify-end space-x-4">
          <button
            id="modal-cancel"
            class="px-6 py-2.5 border border-gray-300 rounded-button text-gray-700 font-medium hover:bg-gray-50 transition-all !rounded-button"
          >
            关闭
          </button>
          <button
            id="modal-confirm"
            class="gradient-bg text-white px-6 py-2.5 rounded-button font-medium shadow-sm hover:shadow-md transition-all !rounded-button"
          >
            确认
          </button>
        </div>
      </div>
    </div>
    <script id="upload-handler">
      document.addEventListener("DOMContentLoaded", function () {
        const uploadArea = document.getElementById("upload-area");
        const fileInput = document.getElementById("file-input");
        const selectFileBtn = document.getElementById("select-file-btn");
        const uploadProgressContainer = document.getElementById(
          "upload-progress-container",
        );
        const uploadProgressList = document.getElementById("upload-progress-list");
        selectFileBtn.addEventListener("click", function (e) {
          e.stopPropagation(); // 阻止事件冒泡
          fileInput.click();
        });
        const reviewProcessContainer = document.getElementById(
          "review-process-container",
        );
        const reviewResultContainer = document.getElementById(
          "review-result-container",
        );
        const reviewResultList = document.getElementById("review-result-list");
        const noRecords = document.getElementById("no-records");
        // 拖拽上传
        uploadArea.addEventListener("dragover", function (e) {
          e.preventDefault();
          uploadArea.classList.add("dragging");
        });
        uploadArea.addEventListener("dragleave", function () {
          uploadArea.classList.remove("dragging");
        });
        uploadArea.addEventListener("drop", function (e) {
          e.preventDefault();
          uploadArea.classList.remove("dragging");
          if (e.dataTransfer.files.length > 0) {
            handleFiles(e.dataTransfer.files);
          }
        });
        // 点击上传区域（但不包括按钮）
        uploadArea.addEventListener("click", function (e) {
          // 如果点击的是按钮，不处理（按钮有自己的事件处理器）
          if (e.target.closest('#select-file-btn')) {
            return;
          }
          fileInput.click();
        });
        fileInput.addEventListener("change", function () {
          if (fileInput.files.length > 0) {
            handleFiles(fileInput.files);
            noRecords.classList.add("hidden");
            // 重置文件输入框，避免重复上传问题
            fileInput.value = '';
          }
        });
        function handleFiles(files) {
          // 清理之前的状态
          uploadProgressContainer.classList.remove("hidden");
          reviewProcessContainer.classList.add("hidden");
          reviewResultContainer.classList.add("hidden");
          document.getElementById("json-data-container").classList.add("hidden");
          uploadProgressList.innerHTML = "";

          // 隐藏所有审核详情步骤
          const step1Details = document.getElementById("step-1-details");
          const step2Details = document.getElementById("step-2-details");
          const step3Details = document.getElementById("step-3-details");
          if (step1Details) step1Details.style.display = "none";
          if (step2Details) step2Details.style.display = "none";
          if (step3Details) step3Details.style.display = "none";

          // 清空之前的审核结果，开始新的上传
          window.currentAuditResults = [];

          // 验证文件
          const validFiles = Array.from(files).filter(file => {
            const validTypes = [
              'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp',
              'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
              'application/pdf',
              'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              'text/plain'
            ];

            const isValidType = validTypes.includes(file.type) ||
                               file.name.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|mp4|avi|mov|wmv|flv|pdf|doc|docx|xls|xlsx|ppt|pptx|txt)$/);

            if (!isValidType) {
              alert(`不支持的文件类型: ${file.name}`);
              return false;
            }

            if (file.size > 500 * 1024 * 1024) { // 500MB
              alert(`文件过大: ${file.name} (最大支持500MB)`);
              return false;
            }

            return true;
          });

          if (validFiles.length === 0) {
            uploadProgressContainer.classList.add("hidden");
            return;
          }

          validFiles.forEach((file, index) => {
            const fileId = Date.now() + index;
            createProgressItem(file, fileId);
            realUpload(file, fileId);
          });
        }
        function createProgressItem(file, fileId) {
          const fileSize = formatFileSize(file.size);
          const fileType = getFileType(file.type);
          const fileIcon = getFileIcon(fileType);
          const progressItem = document.createElement("div");
          progressItem.id = `progress-item-${fileId}`;
          progressItem.className =
            "bg-white rounded-xl p-4 shadow-sm flex items-center";
          progressItem.innerHTML = `
      <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
      <i class="${fileIcon} text-primary"></i>
      </div>
      <div class="flex-grow mr-4">
      <div class="flex justify-between mb-1">
      <span class="text-sm font-medium text-gray-800">${file.name}</span>
      <span class="text-xs text-gray-500" id="progress-percentage-${fileId}">0%</span>
      </div>
      <div class="flex justify-between items-center">
      <div class="w-full bg-gray-200 rounded-full h-1.5 mr-4">
      <div id="progress-bar-${fileId}" class="gradient-bg h-1.5 rounded-full" style="width: 0%"></div>
      </div>
      <span class="text-xs text-gray-500 whitespace-nowrap">${fileSize}</span>
      </div>
      <div class="flex justify-between mt-1">
      <span class="text-xs text-gray-500" id="upload-speed-${fileId}">0 KB/s</span>
      <span class="text-xs text-gray-500" id="upload-time-${fileId}">剩余时间: 计算中...</span>
      </div>
      </div>
      <button class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 cursor-pointer" onclick="cancelUpload(${fileId})">
      <i class="ri-close-line text-gray-500"></i>
      </button>
      `;
          uploadProgressList.appendChild(progressItem);
        }

        // 真实上传函数
        async function realUpload(file, fileId) {
          const progressBar = document.getElementById(`progress-bar-${fileId}`);
          const progressPercentage = document.getElementById(`progress-percentage-${fileId}`);
          const uploadSpeed = document.getElementById(`upload-speed-${fileId}`);
          const uploadTime = document.getElementById(`upload-time-${fileId}`);

          try {
            // 显示审核流程区域并滚动
            showReviewProcess();

            // 更新审核流程状态：开始上传
            updateReviewStep(1, 'active', '文件上传', '正在上传文件...');
            updateStatusMessage('文件正在上传，请稍候...');

            const formData = new FormData();
            formData.append('file', file);

            const startTime = Date.now();

            // 显示上传状态
            progressBar.style.width = '100%';
            progressPercentage.textContent = '100%';
            uploadSpeed.textContent = '正在处理...';
            uploadTime.textContent = '请稍候';

            // 更新审核流程：开始处理
            updateReviewStep(1, 'active', '文件上传', '正在上传...');
            updateStatusMessage('正在上传文件并进行分析...');

            // 第一步：发送文件进行素材解析
            const uploadResponse = await fetch('/upload', {
              method: 'POST',
              body: formData
            });

            if (uploadResponse.ok) {
              const parseResult = await uploadResponse.json();

              // 第1步：文件上传完成
              updateReviewStep(1, 'completed', '文件上传', '上传完成');
              showStep1Details();

              // 第2步：素材解析完成，立即展示解析结果
              updateReviewStep(2, 'completed', '素材解析', '解析完成');
              showAnalysisDetails(parseResult);
              showStep2Details();
              updateStatusMessage('素材解析完成，正在进行AI审核...');

              // 第3步：开始AI审核
              updateReviewStep(3, 'active', 'AI审核', '正在调用AI审核引擎...');

              try {
                // 第二步：调用AI审核接口
                console.log('🔍 开始调用AI审核接口, trace_id:', parseResult.trace_id);
                const auditResponse = await fetch('/audit', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    trace_id: parseResult.trace_id
                  })
                });
                console.log('📋 AI审核接口响应状态:', auditResponse.status);

                if (auditResponse.ok) {
                  const auditResult = await auditResponse.json();
                  console.log('✅ AI审核完成，结果:', auditResult);
                  const finalResult = auditResult.audit_result;

                  // 第3步：AI审核完成
                  const isViolation = finalResult?.is_violation || false;
                  console.log('📊 审核结果 - 是否违规:', isViolation);
                  updateReviewStep(3, 'completed', 'AI审核', isViolation ? '审核不通过' : '审核通过');
                  updateReviewStep(4, 'completed', '审核完成', isViolation ? '发现违规内容' : '审核通过');
                  updateStatusMessage(isViolation ? '审核完成，发现违规内容' : '审核完成，内容合规');

                  // 显示最终审核结果
                  showStep3Details(finalResult);

                  // 更新解析结果以包含审核信息
                  parseResult.audit_result = finalResult;

                } else {
                  throw new Error(`AI审核失败: ${auditResponse.status}`);
                }

              } catch (auditError) {
                console.error('❌ AI审核错误:', auditError);
                updateReviewStep(3, 'completed', 'AI审核', '审核失败');
                updateReviewStep(4, 'completed', '审核完成', '审核过程出现错误');
                updateStatusMessage('AI审核失败，请重试');
              }

              // 显示最终完成状态
              const progressItem = document.getElementById(`progress-item-${fileId}`);
              if (progressItem) {
                progressItem.innerHTML = `
                  <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-green-50 mr-4">
                    <i class="ri-check-line text-green-500"></i>
                  </div>
                  <div class="flex-grow">
                    <div class="flex justify-between mb-1">
                      <span class="text-sm font-medium text-gray-800">处理完成</span>
                    </div>
                    <p class="text-sm text-gray-500">文件已成功处理并完成AI审核</p>
                  </div>
                `;

                // 存储审核结果
                window.currentAuditResults = window.currentAuditResults || [];
                window.currentAuditResults.push({
                  fileId: fileId,
                  filename: file.name,
                  result: parseResult
                });

                // 延迟显示审核结果
                setTimeout(() => {
                  progressItem.remove();
                  if (uploadProgressList.children.length === 0) {
                    uploadProgressContainer.classList.add("hidden");
                  }
                  addReviewResult(parseResult, file.name);
                }, 1000);
              }

            } else {
              throw new Error(`上传失败: ${response.status}`);
            }

          } catch (error) {
            console.error('处理错误:', error);

            const progressItem = document.getElementById(`progress-item-${fileId}`);
            if (progressItem) {
              progressItem.innerHTML = `
                <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-red-50 mr-4">
                  <i class="ri-close-line text-red-500"></i>
                </div>
                <div class="flex-grow">
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-800">上传失败</span>
                  </div>
                  <p class="text-sm text-gray-500">错误: ${error.message}</p>
                </div>
              `;
            }
          }
        }

        function simulateUpload(fileId) {
          let progress = 0;
          const progressBar = document.getElementById(`progress-bar-${fileId}`);
          const progressPercentage = document.getElementById(
            `progress-percentage-${fileId}`,
          );
          const uploadSpeed = document.getElementById(`upload-speed-${fileId}`);
          const uploadTime = document.getElementById(`upload-time-${fileId}`);
          const interval = setInterval(() => {
            const increment = Math.random() * 5 + 1;
            progress += increment;
            if (progress >= 100) {
              progress = 100;
              clearInterval(interval);
              setTimeout(() => {
                const progressItem = document.getElementById(
                  `progress-item-${fileId}`,
                );
                if (progressItem) {
                  progressItem.innerHTML = `
      <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-green-50 mr-4">
      <i class="ri-check-line text-green-500"></i>
      </div>
      <div class="flex-grow">
      <div class="flex justify-between mb-1">
      <span class="text-sm font-medium text-gray-800">上传成功</span>
      </div>
      <p class="text-sm text-gray-500">文件已成功上传，即将进入审核流程...</p>
      </div>
      `;
                  setTimeout(() => {
                    progressItem.remove();
                    if (uploadProgressList.children.length === 0) {
                      uploadProgressContainer.classList.add("hidden");
                      reviewProcessContainer.classList.remove("hidden");
                      startReviewProcess();
                    }
                  }, 1500);
                }
              }, 500);
            }
            progressBar.style.width = `${progress}%`;
            progressPercentage.textContent = `${Math.round(progress)}%`;
            const speed = Math.random() * 500 + 100;
            uploadSpeed.textContent = `${Math.round(speed)} KB/s`;
            const remainingTime = Math.round((100 - progress) / increment);
            uploadTime.textContent = `剩余时间: ${remainingTime} 秒`;
          }, 300);
          window.uploadIntervals = window.uploadIntervals || {};
          window.uploadIntervals[fileId] = interval;
        }
        // 添加到全局作用域，以便HTML中的onclick调用
        window.cancelUpload = function (fileId) {
          if (window.uploadIntervals && window.uploadIntervals[fileId]) {
            clearInterval(window.uploadIntervals[fileId]);
            delete window.uploadIntervals[fileId];
          }
          const progressItem = document.getElementById(`progress-item-${fileId}`);
          if (progressItem) {
            progressItem.remove();
          }
          if (uploadProgressList.children.length === 0) {
            uploadProgressContainer.classList.add("hidden");
          }
        };

        // 将工具函数移到全局作用域
        window.formatFileSize = function(bytes) {
          if (bytes === 0) return "0 Bytes";
          const k = 1024;
          const sizes = ["Bytes", "KB", "MB", "GB"];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
        };

        window.getFileType = function(mimeType) {
          if (mimeType.startsWith("image/")) return "image";
          if (mimeType.startsWith("video/")) return "video";
          if (mimeType.startsWith("application/pdf")) return "pdf";
          if (mimeType.includes("word") || mimeType.includes("document"))
            return "doc";
          if (mimeType.includes("excel") || mimeType.includes("sheet")) return "xls";
          if (mimeType.includes("powerpoint") || mimeType.includes("presentation"))
            return "ppt";
          return "file";
        };

        window.getFileIcon = function(fileType) {
          switch (fileType) {
            case "image":
              return "ri-image-line";
            case "video":
              return "ri-video-line";
            case "pdf":
              return "ri-file-pdf-line";
            case "doc":
              return "ri-file-word-line";
            case "xls":
              return "ri-file-excel-line";
            case "ppt":
              return "ri-file-ppt-line";
            default:
              return "ri-file-line";
          }
        };
      });
    </script>
    <script id="review-process-handler">
      document.addEventListener("DOMContentLoaded", function () {
        // 真实审核流程
        window.startRealReviewProcess = function () {
          const step1Circle = document.getElementById("step-1-circle");
          const step2Circle = document.getElementById("step-2-circle");
          const step3Circle = document.getElementById("step-3-circle");
          const step4Circle = document.getElementById("step-4-circle");
          const step1Text = document.getElementById("step-1-text");
          const step2Text = document.getElementById("step-2-text");
          const step3Text = document.getElementById("step-3-text");
          const step4Text = document.getElementById("step-4-text");
          const step1Time = document.getElementById("step-1-time");
          const step2Time = document.getElementById("step-2-time");
          const step3Time = document.getElementById("step-3-time");
          const step4Time = document.getElementById("step-4-time");
          const connector1 = document.getElementById("connector-1");
          const connector2 = document.getElementById("connector-2");
          const connector3 = document.getElementById("connector-3");
          const statusMessage = document.getElementById("status-message");

          // 步骤1完成
          step1Circle.innerHTML = '<i class="ri-check-line"></i>';
          step1Circle.classList.add("completed");

          // 步骤2激活
          setTimeout(() => {
            step2Circle.classList.add("active");
            step2Text.classList.remove("text-gray-500");
            step2Text.classList.add("text-primary");
            connector1.classList.add("active");
            const now = new Date();
            step2Time.textContent = formatDateTime(now);
            statusMessage.textContent = "AI系统正在分析文件内容，包括OCR识别、人脸检测、语音识别等...";

            // 步骤2完成
            setTimeout(() => {
              step2Circle.innerHTML = '<i class="ri-check-line"></i>';
              step2Circle.classList.add("completed");

              // 步骤3激活
              step3Circle.classList.add("active");
              step3Text.classList.remove("text-gray-500");
              step3Text.classList.add("text-primary");
              connector2.classList.add("active");
              const now = new Date();
              step3Time.textContent = formatDateTime(now);
              statusMessage.textContent = "AI分析完成，正在进行风险评估和合规检查...";

              // 步骤3完成
              setTimeout(() => {
                step3Circle.innerHTML = '<i class="ri-check-line"></i>';
                step3Circle.classList.add("completed");

                // 步骤4激活
                step4Circle.classList.add("active");
                step4Text.classList.remove("text-gray-500");
                step4Text.classList.add("text-primary");
                connector3.classList.add("active");
                const now = new Date();
                step4Time.textContent = formatDateTime(now);
                statusMessage.textContent = "审核已完成，请查看详细的AI审核结果";

                // 显示真实审核结果和JSON数据
                setTimeout(() => {
                  document.getElementById("json-data-container").classList.remove("hidden");
                  displayJsonData();
                  document.getElementById("review-result-container").classList.remove("hidden");
                  generateRealReviewResults();
                }, 1000);
              }, 2000);
            }, 2000);
          }, 1000);
        };

        // 显示JSON数据
        window.displayJsonData = function () {
          const jsonDisplay = document.getElementById("json-display");
          const copyBtn = document.getElementById("copy-json-btn");

          if (window.currentAuditResults && window.currentAuditResults.length > 0) {
            // 合并所有审核结果
            const allResults = window.currentAuditResults.map(item => ({
              filename: item.filename,
              trace_id: item.result.trace_id,
              upload_time: item.result.audit_result.audit_time,
              file_info: item.result.audit_result.file_info,
              analysis_result: {
                ocr_result: item.result.audit_result.ocr_result,
                face_detection: item.result.audit_result.face_detection,
                asr_result: item.result.audit_result.asr_result,
                qr_code_result: item.result.audit_result.qr_code_result,
                risk_evaluation: item.result.audit_result.risk_evaluation,
                compliance_check: item.result.audit_result.compliance_check,
                analysis_method: item.result.audit_result.analysis_method
              },
              // 添加审核API结果
              audit_api_result: item.result.audit_result.audit_api_result,
              is_violation: item.result.audit_result.is_violation,
              rule_results: item.result.audit_result.rule_results,
              suggestion: item.result.audit_result.suggestion
            }));

            const jsonData = {
              total_files: allResults.length,
              analysis_timestamp: new Date().toISOString(),
              results: allResults
            };

            jsonDisplay.textContent = JSON.stringify(jsonData, null, 2);

            // 更新审核详情显示
            if (allResults.length > 0 && allResults[0].audit_api_result) {
              window.updateAuditDetails(allResults[0].audit_api_result);
            }

            // 复制功能
            copyBtn.onclick = function() {
              navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2)).then(() => {
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="ri-check-line mr-1"></i>已复制';
                copyBtn.classList.add('bg-green-50', 'text-green-600');
                copyBtn.classList.remove('bg-blue-50', 'text-blue-600');

                setTimeout(() => {
                  copyBtn.innerHTML = originalText;
                  copyBtn.classList.remove('bg-green-50', 'text-green-600');
                  copyBtn.classList.add('bg-blue-50', 'text-blue-600');
                }, 2000);
              }).catch(() => {
                alert('复制失败，请手动选择文本复制');
              });
            };
          } else {
            jsonDisplay.textContent = '暂无数据';
          }
        };

        window.startReviewProcess = function () {
          const step1Circle = document.getElementById("step-1-circle");
          const step2Circle = document.getElementById("step-2-circle");
          const step3Circle = document.getElementById("step-3-circle");
          const step4Circle = document.getElementById("step-4-circle");
          const step1Text = document.getElementById("step-1-text");
          const step2Text = document.getElementById("step-2-text");
          const step3Text = document.getElementById("step-3-text");
          const step4Text = document.getElementById("step-4-text");
          const step1Time = document.getElementById("step-1-time");
          const step2Time = document.getElementById("step-2-time");
          const step3Time = document.getElementById("step-3-time");
          const step4Time = document.getElementById("step-4-time");
          const connector1 = document.getElementById("connector-1");
          const connector2 = document.getElementById("connector-2");
          const connector3 = document.getElementById("connector-3");
          const statusMessage = document.getElementById("status-message");
          // 模拟素材解析过程
          setTimeout(() => {
            // 步骤1完成
            step1Circle.innerHTML = '<i class="ri-check-line"></i>';
            step1Circle.classList.add("completed");
            // 步骤2激活
            step2Circle.classList.add("active");
            step2Text.classList.remove("text-gray-500");
            step2Text.classList.add("text-primary");
            connector1.classList.add("active");
            const now = new Date();
            step2Time.textContent = formatDateTime(now);
            statusMessage.textContent = "正在使用AI模型解析素材内容，请稍候...";
            // 模拟素材解析完成
            setTimeout(() => {
              // 步骤2完成
              step2Circle.innerHTML = '<i class="ri-check-line"></i>';
              step2Circle.classList.add("completed");
              // 步骤3激活
              step3Circle.classList.add("active");
              step3Text.classList.remove("text-gray-500");
              step3Text.classList.add("text-primary");
              connector2.classList.add("active");
              const now = new Date();
              step3Time.textContent = formatDateTime(now);
              statusMessage.textContent = "素材解析完成，开始AI审核中...";
              // 模拟AI审核
              setTimeout(() => {
                // 步骤3完成
                step3Circle.innerHTML = '<i class="ri-check-line"></i>';
                step3Circle.classList.add("completed");
                // 步骤4激活
                step4Circle.classList.add("active");
                step4Text.classList.remove("text-gray-500");
                step4Text.classList.add("text-primary");
                connector3.classList.add("active");
                const now = new Date();
                step4Time.textContent = formatDateTime(now);
                statusMessage.textContent = "审核已完成，请查看审核结果";
                // 显示审核结果
                setTimeout(() => {
                  document
                    .getElementById("review-result-container")
                    .classList.remove("hidden");
                  generateReviewResults();
                }, 1000);
              }, 5000); // AI审核时间
            }, 3000); // 素材解析时间
          }, 2000); // 初始延迟
        };
        function formatDateTime(date) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          return `${year}-${month}-${day} ${hours}:${minutes}`;
        }
        // 生成真实审核结果
        window.generateRealReviewResults = function () {
          const reviewResultList = document.getElementById("review-result-list");
          reviewResultList.innerHTML = "";

          if (window.currentAuditResults && window.currentAuditResults.length > 0) {
            window.currentAuditResults.forEach((auditData, index) => {
              const result = auditData.result;
              const audit = result.audit_result;

              // 构造结果数据
              const resultItem = {
                id: auditData.fileId,
                fileName: auditData.filename,
                fileType: getFileTypeFromName(auditData.filename),
                fileSize: formatFileSize(result.size),
                status: audit.compliance_check ? "approved" : "rejected",
                reviewer: "AI系统",
                reviewTime: new Date(audit.audit_time).toLocaleString('zh-CN'),
                comments: generateAIComments(audit),
                suggestions: generateAISuggestions(audit),
                auditDetails: audit,
                // 添加审核API结果
                isViolation: audit.is_violation || false,
                ruleResults: (audit.audit_api_result && audit.audit_api_result.rule_results) || [],
                auditSuggestion: audit.suggestion || ""
              };

              const resultElement = createRealResultItem(resultItem);
              reviewResultList.appendChild(resultElement);
            });
          } else {
            // 如果没有真实结果，显示提示
            reviewResultList.innerHTML = `
              <div class="bg-white rounded-2xl p-6 shadow-sm text-center">
                <div class="w-16 h-16 mx-auto flex items-center justify-center rounded-full bg-gray-100 mb-4">
                  <i class="ri-file-list-3-line ri-2x text-gray-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">暂无审核结果</h3>
                <p class="text-gray-500">请先上传文件进行审核</p>
              </div>
            `;
          }
        };

        function generateAIComments(audit) {
          let comments = [];

          if (audit.ocr_result && audit.ocr_result.length > 0) {
            comments.push(`检测到${audit.ocr_result.length}个文字区域`);
          }

          if (audit.face_detection && audit.face_detection.length > 0) {
            comments.push(`检测到${audit.face_detection.length}个人脸`);
          }

          if (audit.asr_result && audit.asr_result.length > 0) {
            comments.push(`语音识别完成，检测到${audit.asr_result.length}段语音`);
          }

          if (audit.qr_code_result && audit.qr_code_result.length > 0) {
            comments.push(`检测到${audit.qr_code_result.length}个二维码`);
          }

          comments.push(`风险等级: ${audit.risk_evaluation}`);
          comments.push(`合规检查: ${audit.compliance_check ? '通过' : '未通过'}`);

          return comments.join('；');
        }

        function generateAISuggestions(audit) {
          let suggestions = [];

          if (audit.risk_evaluation === 'high') {
            suggestions.push('建议人工复审高风险内容');
          }

          if (audit.ocr_result && audit.ocr_result.some(text => text.includes('贷款') || text.includes('投资'))) {
            suggestions.push('检测到金融相关词汇，请确保符合监管要求');
          }

          if (!audit.compliance_check) {
            suggestions.push('内容不符合合规要求，建议修改后重新提交');
          }

          return suggestions;
        }

        window.getFileTypeFromName = function(filename) {
          const ext = filename.split('.').pop().toLowerCase();
          if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)) return 'image';
          if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(ext)) return 'video';
          if (ext === 'pdf') return 'pdf';
          if (['doc', 'docx'].includes(ext)) return 'doc';
          if (['xls', 'xlsx'].includes(ext)) return 'xls';
          if (['ppt', 'pptx'].includes(ext)) return 'ppt';
          return 'file';
        };

        window.generateReviewResults = function () {
          const reviewResultList = document.getElementById("review-result-list");
          reviewResultList.innerHTML = "";
          // 模拟3个审核结果
          const results = [
            {
              id: 1,
              fileName: "营销宣传视频.mp4",
              fileType: "video",
              fileSize: "35.4 MB",
              status: "approved",
              reviewer: "李审核",
              reviewTime: "2025-07-04 15:05",
              comments:
                "视频内容符合银行宣传规范，画面清晰，音频质量良好，可以用于线上营销活动。",
              suggestions: [],
            },
            {
              id: 2,
              fileName: "贷款产品介绍.pdf",
              fileType: "pdf",
              fileSize: "2.8 MB",
              status: "rejected",
              reviewer: "王审核",
              reviewTime: "2025-07-04 15:10",
              comments: "文档中部分条款描述不够清晰，可能导致客户误解。",
              suggestions: [
                "第3页中关于利率的描述需要更加明确",
                "第5页中的贷款期限说明需要补充提前还款的相关条件",
                "封面设计需要符合最新的品牌规范",
              ],
            },
            {
              id: 3,
              fileName: "客户权益说明.jpg",
              fileType: "image",
              fileSize: "1.2 MB",
              status: "approved",
              reviewer: "赵审核",
              reviewTime: "2025-07-04 15:15",
              comments: "图片内容清晰，表述准确，符合监管要求。",
              suggestions: [],
            },
          ];
          results.forEach((result) => {
            const resultItem = createResultItem(result);
            reviewResultList.appendChild(resultItem);
          });
        };
        function createRealResultItem(result) {
          const resultItem = document.createElement("div");
          resultItem.className = "bg-white rounded-2xl p-6 shadow-sm";
          const statusColor = result.status === "approved" ? "text-green-500 bg-green-50" : "text-red-500 bg-red-50";
          const statusIcon = result.status === "approved" ? "ri-checkbox-circle-line" : "ri-close-circle-line";
          const statusText = result.status === "approved" ? "AI审核通过" : "AI审核不通过";
          const fileIcon = getFileIconByType(result.fileType);

          resultItem.innerHTML = `
            <div class="flex justify-between items-start mb-4">
              <div class="flex items-center">
                <div class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
                  <i class="${fileIcon} ri-lg text-primary"></i>
                </div>
                <div>
                  <h4 class="text-lg font-medium text-gray-800">${result.fileName}</h4>
                  <p class="text-sm text-gray-500">${result.fileSize} • ${result.reviewTime}</p>
                </div>
              </div>
              <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
                <i class="${statusIcon} mr-1"></i>
                <span class="text-sm font-medium">${statusText}</span>
              </div>
            </div>
            <div class="pl-16">
              <div class="mb-4">
                <h5 class="text-sm font-medium text-gray-700 mb-2">AI审核结果</h5>
                <p class="text-sm text-gray-600">${result.comments}</p>
              </div>
              ${result.isViolation && result.ruleResults && result.ruleResults.length > 0 ? `
                <div class="mb-4">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">违规原因</h5>
                  <div class="bg-red-50 rounded-lg p-3 border border-red-200">
                    <div class="overflow-x-auto">
                      <table class="min-w-full">
                        <thead>
                          <tr class="border-b border-red-200">
                            <th class="px-2 py-2 text-left text-xs font-medium text-red-700">规则ID</th>
                            <th class="px-2 py-2 text-left text-xs font-medium text-red-700">规则名称</th>
                            <th class="px-2 py-2 text-left text-xs font-medium text-red-700">级别</th>
                            <th class="px-2 py-2 text-left text-xs font-medium text-red-700">规则描述</th>
                            <th class="px-2 py-2 text-left text-xs font-medium text-red-700">违规原因</th>
                          </tr>
                        </thead>
                        <tbody>
                          ${result.ruleResults.filter(rule => rule.is_violation).map(rule => `
                            <tr class="border-b border-red-100">
                              <td class="px-2 py-2 text-xs text-red-600">${rule.rule_id}</td>
                              <td class="px-2 py-2 text-xs text-red-600">${rule.rule_name}</td>
                              <td class="px-2 py-2 text-xs text-red-600">${rule.priority}</td>
                              <td class="px-2 py-2 text-xs text-red-600">${rule.rule_description}</td>
                              <td class="px-2 py-2 text-xs text-red-600">${rule.reason}</td>
                            </tr>
                          `).join("")}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              ` : ""}
              ${result.isViolation && result.auditSuggestion ? `
                <div class="mb-4">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">建议</h5>
                  <div class="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                    <div class="text-sm text-yellow-800 whitespace-pre-line">${result.auditSuggestion}</div>
                  </div>
                </div>
              ` : ""}
              ${!result.isViolation && result.suggestions.length > 0 ? `
                <div class="mb-4">
                  <h5 class="text-sm font-medium text-gray-700 mb-2">建议</h5>
                  <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
                    ${result.suggestions.map(suggestion => `<li>${suggestion}</li>`).join("")}
                  </ul>
                </div>
              ` : ""}
              <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">审核方式: ${result.reviewer}</div>
                <div class="flex space-x-2">
                  <button class="px-4 py-1.5 gradient-bg text-white rounded-button text-sm font-medium shadow-sm hover:shadow-md transition-all !rounded-button" onclick="showRealDetailModal(${result.id})">查看详情</button>
                </div>
              </div>
            </div>
          `;
          return resultItem;
        }

        function createResultItem(result) {
          const resultItem = document.createElement("div");
          resultItem.className = "bg-white rounded-2xl p-6 shadow-sm";
          const statusColor =
            result.status === "approved"
              ? "text-green-500 bg-green-50"
              : "text-red-500 bg-red-50";
          const statusIcon =
            result.status === "approved"
              ? "ri-checkbox-circle-line"
              : "ri-close-circle-line";
          const statusText = result.status === "approved" ? "审核通过" : "审核不通过";
          const fileIcon = getFileIconByType(result.fileType);
          resultItem.innerHTML = `
      <div class="flex justify-between items-start mb-4">
      <div class="flex items-center">
      <div class="w-12 h-12 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
      <i class="${fileIcon} ri-lg text-primary"></i>
      </div>
      <div>
      <h4 class="text-lg font-medium text-gray-800">${result.fileName}</h4>
      <p class="text-sm text-gray-500">${result.fileSize} • ${result.reviewTime}</p>
      </div>
      </div>
      <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
      <i class="${statusIcon} mr-1"></i>
      <span class="text-sm font-medium">${statusText}</span>
      </div>
      </div>
      <div class="pl-16">
      <div class="mb-4">
      <h5 class="text-sm font-medium text-gray-700 mb-2">审核意见</h5>
      <p class="text-sm text-gray-600">${result.comments}</p>
      </div>
      ${
        result.suggestions.length > 0
          ? `
      <div class="mb-4">
      <h5 class="text-sm font-medium text-gray-700 mb-2">修改建议</h5>
      <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
      ${result.suggestions.map((suggestion) => `<li>${suggestion}</li>`).join("")}
      </ul>
      </div>
      `
          : ""
      }
      <div class="flex justify-between items-center">
      <div class="text-sm text-gray-500">审核人员: ${result.reviewer}</div>
      <div class="flex space-x-2">
      <button class="px-4 py-1.5 gradient-bg text-white rounded-button text-sm font-medium shadow-sm hover:shadow-md transition-all !rounded-button" onclick="showDetailModal(${result.id})">查看详情</button>
      </div>
      </div>
      </div>
      `;
          return resultItem;
        }
        function getFileIconByType(fileType) {
          switch (fileType) {
            case "image":
              return "ri-image-line";
            case "video":
              return "ri-video-line";
            case "pdf":
              return "ri-file-pdf-line";
            case "doc":
              return "ri-file-word-line";
            case "xls":
              return "ri-file-excel-line";
            case "ppt":
              return "ri-file-ppt-line";
            default:
              return "ri-file-line";
          }
        }
        // 显示真实详情弹窗
        window.showRealDetailModal = function (resultId) {
          const modal = document.getElementById("review-detail-modal");
          const modalContent = document.getElementById("modal-content");

          // 查找对应的审核结果
          const auditData = window.currentAuditResults.find(item => item.fileId == resultId);
          if (!auditData) {
            alert('未找到审核结果');
            return;
          }

          const result = auditData.result;
          const audit = result.audit_result;
          const fileIcon = getFileIconByType(getFileTypeFromName(auditData.filename));
          const statusColor = audit.compliance_check ? "text-green-500 bg-green-50" : "text-red-500 bg-red-50";
          const statusIcon = audit.compliance_check ? "ri-checkbox-circle-line" : "ri-close-circle-line";
          const statusText = audit.compliance_check ? "AI审核通过" : "AI审核不通过";

          modalContent.innerHTML = `
            <div class="flex items-start">
              <div class="w-14 h-14 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
                <i class="${fileIcon} ri-2x text-primary"></i>
              </div>
              <div class="flex-grow">
                <div class="flex justify-between items-center mb-2">
                  <h4 class="text-lg font-medium text-gray-800">${auditData.filename}</h4>
                  <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
                    <i class="${statusIcon} mr-1"></i>
                    <span class="text-sm font-medium">${statusText}</span>
                  </div>
                </div>
                <p class="text-sm text-gray-500">${formatFileSize(result.size)}</p>
              </div>
            </div>

            <div class="border-t border-gray-200 pt-4 mt-4">
              <!-- AI素材解析详情 -->
              <div class="mb-6">
                <div class="flex items-center justify-between mb-3">
                  <h5 class="text-md font-medium text-gray-800">AI素材解析详情</h5>
                  <button id="toggle-analysis-detail-btn" class="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                    <i class="ri-eye-line mr-1"></i>展开查看
                  </button>
                </div>
                <div id="analysis-detail-collapsed" class="bg-gray-50 rounded-lg p-3 border">
                  <p class="text-sm text-gray-700">AI模型已完成素材解析，包含OCR识别、人脸检测、语音识别等信息</p>
                </div>
                <div id="analysis-detail-expanded" class="bg-gray-50 rounded-lg p-4 border hidden">
                  ${audit.ocr_result && audit.ocr_result.length > 0 ? `
                    <div class="mb-4">
                      <h6 class="text-sm font-medium text-gray-700 mb-2">OCR文字识别结果</h6>
                      <div class="bg-white p-3 rounded-lg border">
                        <ul class="text-sm text-gray-600 space-y-1">
                          ${audit.ocr_result.map(text => `<li>• ${text}</li>`).join("")}
                        </ul>
                      </div>
                    </div>
                  ` : ""}

                  ${audit.face_detection && audit.face_detection.length > 0 ? `
                    <div class="mb-4">
                      <h6 class="text-sm font-medium text-gray-700 mb-2">人脸检测结果</h6>
                      <div class="bg-white p-3 rounded-lg border">
                        <p class="text-sm text-gray-600">检测到 ${audit.face_detection.length} 个人脸</p>
                      </div>
                    </div>
                  ` : ""}

                  ${audit.asr_result && audit.asr_result.length > 0 ? `
                    <div class="mb-4">
                      <h6 class="text-sm font-medium text-gray-700 mb-2">语音识别结果</h6>
                      <div class="bg-white p-3 rounded-lg border">
                        <ul class="text-sm text-gray-600 space-y-1">
                          ${audit.asr_result.map(text => `<li>• ${text}</li>`).join("")}
                        </ul>
                      </div>
                    </div>
                  ` : ""}

                  ${audit.qr_code_result && audit.qr_code_result.length > 0 ? `
                    <div class="mb-4">
                      <h6 class="text-sm font-medium text-gray-700 mb-2">二维码检测结果</h6>
                      <div class="bg-white p-3 rounded-lg border">
                        <p class="text-sm text-gray-600">检测到 ${audit.qr_code_result.length} 个二维码</p>
                      </div>
                    </div>
                  ` : ""}
                </div>
              </div>

              <!-- AI审核详情 -->
              ${audit.audit_api_result && audit.audit_api_result.rule_results && audit.audit_api_result.rule_results.length > 0 ? `
                <div class="mb-6">
                  <h5 class="text-md font-medium text-gray-800 mb-3">AI审核详情</h5>
                  <div class="bg-gray-50 p-4 rounded-lg border">
                    <div class="overflow-x-auto">
                      <table class="min-w-full">
                        <thead>
                          <tr class="border-b border-gray-200">
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700">规则ID</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700">规则名称</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700">级别</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700">审核结果</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700">规则描述</th>
                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-700">违规原因</th>
                          </tr>
                        </thead>
                        <tbody>
                          ${audit.audit_api_result.rule_results.map(rule => `
                            <tr class="border-b border-gray-100">
                              <td class="px-3 py-2 text-xs text-gray-600">${rule.rule_id}</td>
                              <td class="px-3 py-2 text-xs text-gray-600">${rule.rule_name}</td>
                              <td class="px-3 py-2 text-xs text-gray-600">${rule.priority}</td>
                              <td class="px-3 py-2 text-xs">
                                ${rule.is_violation ?
                                  '<i class="ri-close-line text-red-600"></i>' :
                                  '<i class="ri-check-line text-blue-600"></i>'
                                }
                              </td>
                              <td class="px-3 py-2 text-xs text-gray-600">${rule.rule_description}</td>
                              <td class="px-3 py-2 text-xs text-gray-600">${rule.reason || '-'}</td>
                            </tr>
                          `).join("")}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              ` : ""}

              <!-- 风险评估 -->
              <div class="mb-6">
                <h5 class="text-md font-medium text-gray-800 mb-3">风险评估</h5>
                <div class="bg-gray-50 p-4 rounded-lg border">
                  <div class="flex justify-between mb-2">
                    <span class="text-sm text-gray-600">风险等级</span>
                    <span class="text-sm font-medium ${audit.risk_evaluation === 'high' ? 'text-red-500' : audit.risk_evaluation === 'medium' ? 'text-yellow-500' : 'text-green-500'}">${audit.risk_evaluation}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">合规检查</span>
                    <span class="text-sm font-medium ${audit.compliance_check ? 'text-green-500' : 'text-red-500'}">${audit.compliance_check ? '通过' : '未通过'}</span>
                  </div>
                </div>
              </div>

              <!-- 审核信息 -->
              <div class="mb-4">
                <h5 class="text-md font-medium text-gray-800 mb-3">审核信息</h5>
                <div class="bg-gray-50 p-4 rounded-lg border">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">审核时间</span>
                    <span class="text-sm text-gray-800">${new Date(audit.audit_time).toLocaleString('zh-CN')}</span>
                  </div>
                </div>
              </div>
            </div>
          `;

          modal.classList.remove("hidden");

          // 添加展开/折叠功能
          const toggleBtn = document.getElementById("toggle-analysis-detail-btn");
          const collapsedView = document.getElementById("analysis-detail-collapsed");
          const expandedView = document.getElementById("analysis-detail-expanded");

          if (toggleBtn) {
            toggleBtn.addEventListener("click", function() {
              if (expandedView.classList.contains("hidden")) {
                // 展开
                collapsedView.classList.add("hidden");
                expandedView.classList.remove("hidden");
                toggleBtn.innerHTML = '<i class="ri-eye-off-line mr-1"></i>收起';
              } else {
                // 收起
                expandedView.classList.add("hidden");
                collapsedView.classList.remove("hidden");
                toggleBtn.innerHTML = '<i class="ri-eye-line mr-1"></i>展开查看';
              }
            });
          }

          // 统一显示下载审核报告按钮
          const confirmButton = document.getElementById("modal-confirm");
          confirmButton.textContent = "下载审核报告";
        };

        // 添加到全局作用域，以便HTML中的onclick调用
        window.showDetailModal = function (resultId) {
          const modal = document.getElementById("review-detail-modal");
          const modalContent = document.getElementById("modal-content");
          // 模拟获取详细信息
          const detailInfo = {
            id: resultId,
            fileName:
              resultId === 1
                ? "营销宣传视频.mp4"
                : resultId === 2
                  ? "贷款产品介绍.pdf"
                  : "客户权益说明.jpg",
            fileType: resultId === 1 ? "video" : resultId === 2 ? "pdf" : "image",
            fileSize:
              resultId === 1 ? "35.4 MB" : resultId === 2 ? "2.8 MB" : "1.2 MB",
            uploadTime: "2025-07-04 14:30",
            checkTime: "2025-07-04 14:35",
            reviewStartTime: "2025-07-04 14:40",
            reviewEndTime:
              resultId === 1
                ? "2025-07-04 15:05"
                : resultId === 2
                  ? "2025-07-04 15:10"
                  : "2025-07-04 15:15",
            status: resultId === 2 ? "rejected" : "approved",
            reviewer:
              resultId === 1 ? "李审核" : resultId === 2 ? "王审核" : "赵审核",
            department: "内容审核部",
            comments:
              resultId === 1
                ? "视频内容符合银行宣传规范，画面清晰，音频质量良好，可以用于线上营销活动。"
                : resultId === 2
                  ? "文档中部分条款描述不够清晰，可能导致客户误解。"
                  : "图片内容清晰，表述准确，符合监管要求。",
            checkItems: [
              {
                name: "内容合规性",
                result: resultId === 2 ? "不通过" : "通过",
                comments:
                  resultId === 2 ? "部分内容描述不够清晰" : "内容符合监管要求",
              },
              { name: "品牌一致性", result: "通过", comments: "符合品牌规范" },
              {
                name: "信息准确性",
                result: resultId === 2 ? "不通过" : "通过",
                comments: resultId === 2 ? "第3页利率描述不准确" : "信息准确无误",
              },
              { name: "格式规范", result: "通过", comments: "格式符合要求" },
            ],
          };
          const fileIcon = getFileIconByType(detailInfo.fileType);
          const statusColor =
            detailInfo.status === "approved"
              ? "text-green-500 bg-green-50"
              : "text-red-500 bg-red-50";
          const statusIcon =
            detailInfo.status === "approved"
              ? "ri-checkbox-circle-line"
              : "ri-close-circle-line";
          const statusText =
            detailInfo.status === "approved" ? "审核通过" : "审核不通过";
          modalContent.innerHTML = `
      <div class="flex items-start">
      <div class="w-14 h-14 flex items-center justify-center rounded-lg bg-blue-50 mr-4">
      <i class="${fileIcon} ri-2x text-primary"></i>
      </div>
      <div class="flex-grow">
      <div class="flex justify-between items-center mb-2">
      <h4 class="text-lg font-medium text-gray-800">${detailInfo.fileName}</h4>
      <div class="flex items-center ${statusColor} px-3 py-1 rounded-full">
      <i class="${statusIcon} mr-1"></i>
      <span class="text-sm font-medium">${statusText}</span>
      </div>
      </div>
      <p class="text-sm text-gray-500">${detailInfo.fileSize}</p>
      </div>
      </div>
      <div class="border-t border-gray-200 pt-4 mt-4">
      <h5 class="text-md font-medium text-gray-800 mb-3">审核时间线</h5>
      <div class="space-y-3">
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-upload-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">文件上传</p>
      <p class="text-xs text-gray-500">${detailInfo.uploadTime}</p>
      </div>
      </div>
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-shield-check-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">素材解析</p>
      <p class="text-xs text-gray-500">${detailInfo.checkTime}</p>
      </div>
      </div>
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-user-search-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">AI审核开始</p>
      <p class="text-xs text-gray-500">${detailInfo.reviewStartTime}</p>
      </div>
      </div>
      <div class="flex">
      <div class="w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 mt-0.5">
      <i class="ri-check-double-line text-xs text-primary"></i>
      </div>
      <div class="ml-3">
      <p class="text-sm font-medium text-gray-700">审核完成</p>
      <p class="text-xs text-gray-500">${detailInfo.reviewEndTime}</p>
      </div>
      </div>
      </div>
      </div>
      <div class="border-t border-gray-200 pt-4 mt-4">
      <h5 class="text-md font-medium text-gray-800 mb-3">审核详情</h5>
      <div class="mb-4">
      <div class="flex justify-between mb-2">
      <p class="text-sm text-gray-600">审核人员</p>
      <p class="text-sm font-medium text-gray-800">${detailInfo.reviewer}</p>
      </div>
      <div class="flex justify-between mb-2">
      <p class="text-sm text-gray-600">所属部门</p>
      <p class="text-sm font-medium text-gray-800">${detailInfo.department}</p>
      </div>
      </div>
      <div class="mb-4">
      <p class="text-sm font-medium text-gray-700 mb-2">审核意见</p>
      <p class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">${detailInfo.comments}</p>
      </div>
      </div>
      <div class="border-t border-gray-200 pt-4 mt-4">
      <h5 class="text-md font-medium text-gray-800 mb-3">审核项目</h5>
      <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
      <thead>
      <tr>
      <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核项</th>
      <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结果</th>
      <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
      </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
      ${detailInfo.checkItems
        .map(
          (item) => `
      <tr>
      <td class="px-4 py-3 text-sm text-gray-700">${item.name}</td>
      <td class="px-4 py-3 text-sm ${item.result === "通过" ? "text-green-500" : "text-red-500"}">${item.result}</td>
      <td class="px-4 py-3 text-sm text-gray-600">${item.comments}</td>
      </tr>
      `,
        )
        .join("")}
      </tbody>
      </table>
      </div>
      </div>
      `;
          modal.classList.remove("hidden");
          // 统一显示下载审核报告按钮
          const confirmButton = document.getElementById("modal-confirm");
          confirmButton.textContent = "下载审核报告";
        };

        // 关闭弹窗
        document.getElementById("close-modal").addEventListener("click", function () {
          document.getElementById("review-detail-modal").classList.add("hidden");
        });
        document
          .getElementById("modal-cancel")
          .addEventListener("click", function () {
            document.getElementById("review-detail-modal").classList.add("hidden");
          });
        document
          .getElementById("modal-confirm")
          .addEventListener("click", function () {
            // 下载审核报告
            downloadAuditReport();
            document.getElementById("review-detail-modal").classList.add("hidden");
          });
      });
    </script>

    <!-- 审核详情处理脚本 -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // 处理分析结果展开/折叠
        const toggleBtn = document.getElementById("toggle-analysis-btn");
        const collapsedView = document.getElementById("analysis-result-collapsed");
        const expandedView = document.getElementById("analysis-result-expanded");

        if (toggleBtn) {
          toggleBtn.addEventListener("click", function() {
            if (expandedView.classList.contains("hidden")) {
              // 展开
              collapsedView.classList.add("hidden");
              expandedView.classList.remove("hidden");
              toggleBtn.innerHTML = '<i class="ri-eye-off-line mr-1"></i>收起';
            } else {
              // 收起
              expandedView.classList.add("hidden");
              collapsedView.classList.remove("hidden");
              toggleBtn.innerHTML = '<i class="ri-eye-line mr-1"></i>展开查看';
            }
          });
        }

        // 处理复制解析JSON功能
        const copyAnalysisJsonBtn = document.getElementById("copy-analysis-json-btn");
        if (copyAnalysisJsonBtn) {
          copyAnalysisJsonBtn.addEventListener("click", function() {
            // 获取当前的解析结果数据
            if (window.currentAnalysisResult) {
              try {
                // 将解析结果转换为格式化的JSON字符串
                const jsonString = JSON.stringify(window.currentAnalysisResult, null, 2);

                // 复制到剪贴板
                navigator.clipboard.writeText(jsonString).then(function() {
                  // 显示成功提示
                  const originalText = copyAnalysisJsonBtn.innerHTML;
                  copyAnalysisJsonBtn.innerHTML = '<i class="ri-check-line mr-1"></i>已复制';
                  copyAnalysisJsonBtn.className = 'px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg';

                  // 2秒后恢复原状
                  setTimeout(() => {
                    copyAnalysisJsonBtn.innerHTML = originalText;
                    copyAnalysisJsonBtn.className = 'px-3 py-1 text-sm bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors';
                  }, 2000);
                }).catch(function(err) {
                  console.error('复制失败:', err);
                  alert('复制失败，请手动复制');
                });
              } catch (error) {
                console.error('JSON序列化失败:', error);
                alert('数据格式错误，无法复制');
              }
            } else {
              alert('暂无解析结果数据');
            }
          });
        }



        // 移除日志功能，简化审核详情展示
        window.addAuditDetail = function(message) {
          // 空函数，不再显示日志
        };

        // 更新审核详情的函数
        window.updateAuditDetails = function(auditResult) {
          const auditResultIcon = document.getElementById("audit-result-icon");
          const auditResultTitle = document.getElementById("audit-result-title");
          const auditSummary = document.getElementById("audit-summary");
          const auditDetails = document.getElementById("audit-details");
          const auditSuggestion = document.getElementById("audit-suggestion");
          const suggestionText = document.getElementById("suggestion-text");

          if (!auditResult) {
            return;
          }

          const isViolation = auditResult.is_violation;
          const ruleResults = auditResult.rule_results || [];
          const suggestion = auditResult.suggestion || "";

          // 统计通过和违规的规则数量
          const passedRules = ruleResults.filter(rule => !rule.is_violation).length;
          const violatedRules = ruleResults.filter(rule => rule.is_violation).length;
          const totalRules = ruleResults.length;

          // 更新图标和标题
          if (isViolation) {
            auditResultIcon.innerHTML = '<i class="ri-close-line text-red-600 text-sm"></i>';
            auditResultIcon.className = "w-6 h-6 flex items-center justify-center rounded-full bg-red-100 mr-3";
            auditResultTitle.textContent = `完成${totalRules}项规则审核，抱歉，素材审核不通过`;
            auditResultTitle.className = "text-lg font-medium text-red-800";

            auditSummary.className = "bg-red-50 rounded-lg p-4 border border-red-200";
            auditDetails.innerHTML = `<span class="font-medium">审核详情：</span>通过${passedRules}项，违规${violatedRules}项`;
            auditDetails.className = "text-sm text-red-700";

            if (suggestion) {
              auditSuggestion.classList.remove("hidden");
              // 使用innerHTML并添加whitespace-pre-line类来处理换行
              suggestionText.innerHTML = suggestion.replace(/\n/g, '<br>');
            }
          } else {
            auditResultIcon.innerHTML = '<i class="ri-check-line text-green-600 text-sm"></i>';
            auditResultIcon.className = "w-6 h-6 flex items-center justify-center rounded-full bg-green-100 mr-3";
            auditResultTitle.textContent = `完成${totalRules}项规则审核，恭喜你，素材审核通过！`;
            auditResultTitle.className = "text-lg font-medium text-green-800";

            auditSummary.className = "bg-green-50 rounded-lg p-4 border border-green-200";
            auditDetails.innerHTML = `<span class="font-medium">审核详情：</span>通过${passedRules}项，违规${violatedRules}项`;
            auditDetails.className = "text-sm text-green-700";

            auditSuggestion.classList.add("hidden");
          }
        };

        // 下载审核报告函数
        window.downloadAuditReport = function() {
          // 从当前审核结果中获取数据
          const currentResult = window.currentAuditResults && window.currentAuditResults.length > 0 ?
            window.currentAuditResults[0] : null;

          if (!currentResult) {
            alert('无法获取审核数据，请先上传文件进行审核');
            return;
          }

          const audit = currentResult.result.audit_result;
          const filename = currentResult.filename;
          const auditTime = new Date(audit.audit_time);

          // 生成文件名：审核报告_filename_审核时间年月日时分.pdf
          // 去掉文件名中的点和后缀
          const cleanFilename = filename.replace(/\.[^/.]+$/, "").replace(/\./g, "_");
          const timeStr = auditTime.getFullYear() +
            String(auditTime.getMonth() + 1).padStart(2, '0') +
            String(auditTime.getDate()).padStart(2, '0') +
            String(auditTime.getHours()).padStart(2, '0') +
            String(auditTime.getMinutes()).padStart(2, '0');
          const reportFilename = `审核报告_${cleanFilename}_${timeStr}.docx`;

          // 构造报告数据
          const reportData = {
            filename: filename,
            audit_time: audit.audit_time,
            file_info: audit.file_info,
            ocr_result: audit.ocr_result || [],
            face_detection: audit.face_detection || [],
            asr_result: audit.asr_result || [],
            qr_code_result: audit.qr_code_result || [],
            rule_results: (audit.audit_api_result && audit.audit_api_result.rule_results) || [],
            risk_evaluation: audit.risk_evaluation,
            compliance_check: audit.compliance_check,
            is_violation: audit.is_violation,
            suggestion: audit.suggestion || ""
          };

          // 发送到后端生成PDF
          fetch('/generate_audit_report', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(reportData)
          })
          .then(response => {
            if (!response.ok) {
              throw new Error('生成报告失败');
            }
            return response.blob();
          })
          .then(blob => {
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = reportFilename;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            window.URL.revokeObjectURL(url);
          })
          .catch(error => {
            console.error('下载报告失败:', error);
            alert('下载报告失败，请稍后重试');
          });
        };

        // 显示审核流程区域
        window.showReviewProcess = function() {
          const reviewProcessContainer = document.getElementById("review-process-container");
          const jsonDataContainer = document.getElementById("json-data-container");

          reviewProcessContainer.classList.remove("hidden");
          jsonDataContainer.classList.remove("hidden");

          // 滚动到审核流程区域
          setTimeout(() => {
            reviewProcessContainer.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }, 300);
        };

        // 更新审核步骤状态
        window.updateReviewStep = function(stepNumber, status, title, description) {
          const circle = document.getElementById(`step-${stepNumber}-circle`);
          const text = document.getElementById(`step-${stepNumber}-text`);
          const time = document.getElementById(`step-${stepNumber}-time`);

          if (circle && text) {
            // 清除所有状态类
            circle.classList.remove('active', 'completed', 'pending');

            // 添加新状态
            circle.classList.add(status);

            // 更新文本
            text.textContent = title;

            // 更新时间
            if (time) {
              const now = new Date();
              time.textContent = now.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              });
            }
          }
        };

        // 更新状态消息
        window.updateStatusMessage = function(message) {
          const statusMessage = document.getElementById("status-message");
          if (statusMessage) {
            statusMessage.textContent = message;
          }
        };

        // 显示步骤1：素材解析开始
        window.showStep1Details = function() {
          const step1 = document.getElementById("step-1-details");
          if (step1) {
            step1.style.display = "block";
          }
        };

        // 显示步骤1解析结果
        window.showAnalysisDetails = function(result) {
          const audit = result.audit_result || {};

          // 存储解析结果供复制JSON功能使用
          window.currentAnalysisResult = result;

          // 更新折叠视图的描述
          const collapsedView = document.getElementById("analysis-result-collapsed");
          if (collapsedView) {
            const collapsedText = collapsedView.querySelector('p');
            if (collapsedText) {
              collapsedText.textContent = 'AI模型已完成素材解析，包含OCR识别、人脸检测、语音识别等信息';
            }
          }

          // 更新展开视图的详细内容
          const expandedView = document.getElementById("analysis-result-expanded");
          if (expandedView) {
            const spaceDiv = expandedView.querySelector('.space-y-3');
            if (spaceDiv) {
              let analysisHtml = '';

              // OCR结果
              if (audit.ocr_result && audit.ocr_result.length > 0) {
                analysisHtml += `
                  <div class="bg-blue-50 p-3 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">OCR文字识别结果</h4>
                    <div class="text-sm text-blue-700">
                      识别到 ${audit.ocr_result.length} 段文字内容
                    </div>
                  </div>
                `;
              }

              // 人脸检测结果
              if (audit.face_detection && audit.face_detection.length > 0) {
                analysisHtml += `
                  <div class="bg-green-50 p-3 rounded-lg">
                    <h4 class="text-sm font-medium text-green-800 mb-2">人脸检测结果</h4>
                    <div class="text-sm text-green-700">
                      检测到 ${audit.face_detection.length} 个人脸
                    </div>
                  </div>
                `;
              }

              // 语音识别结果
              if (audit.asr_result && audit.asr_result.length > 0) {
                analysisHtml += `
                  <div class="bg-purple-50 p-3 rounded-lg">
                    <h4 class="text-sm font-medium text-purple-800 mb-2">语音识别结果</h4>
                    <div class="text-sm text-purple-700">
                      识别到 ${audit.asr_result.length} 段语音内容
                    </div>
                  </div>
                `;
              }

              if (analysisHtml === '') {
                analysisHtml = `
                  <div class="text-sm text-gray-500 text-center py-4">
                    素材解析完成，未检测到特殊内容
                  </div>
                `;
              }

              spaceDiv.innerHTML = analysisHtml;
            }
          }
        };

        // 显示步骤2：AI审核开始
        window.showStep2Details = function() {
          const step2 = document.getElementById("step-2-details");
          if (step2) {
            step2.style.display = "block";
          }
        };

        // 显示步骤3：AI审核结果
        window.showStep3Details = function(result) {
          const step3 = document.getElementById("step-3-details");
          if (step3) {
            step3.style.display = "block";
          }

          // result 就是 audit_result，直接使用
          const isViolation = result.is_violation || false;

          // 更新审核结果摘要
          const summaryDiv = document.getElementById("audit-result-summary");
          if (summaryDiv) {
            const summaryText = summaryDiv.querySelector('p');
            if (summaryText) {
              if (isViolation) {
                summaryDiv.className = "bg-red-50 rounded-lg p-3 border mb-3";
                summaryText.className = "text-sm text-red-700";
                summaryText.textContent = "审核结果：审核不通过，发现违规内容";
              } else {
                summaryDiv.className = "bg-green-50 rounded-lg p-3 border mb-3";
                summaryText.className = "text-sm text-green-700";
                summaryText.textContent = "审核结果：审核通过，内容合规";
              }
            }
          }


        };

        // 添加审核结果到列表 - 移到全局作用域
        window.addReviewResult = function(result, filename) {
          // 显示审核结果容器
          const reviewResultContainer = document.getElementById("review-result-container");
          const noRecords = document.getElementById("no-records");

          if (reviewResultContainer) {
            reviewResultContainer.classList.remove("hidden");
          }
          if (noRecords) {
            noRecords.classList.add("hidden");
          }

          // 调用现有的生成审核结果函数
          generateRealReviewResults();
        };
      });
    </script>
  </body>
</html>


