<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR文字识别API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background: #0056b3;
        }
        .result-container {
            margin-top: 30px;
            display: none;
        }
        .result-header {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .text-list {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .text-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .text-item:last-child {
            border-bottom: none;
        }
        .confidence {
            color: #666;
            font-size: 12px;
            margin-left: 10px;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .details-table th,
        .details-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .details-table th {
            background-color: #f8f9fa;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 OCR文字识别API测试</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 点击选择图片或拖拽图片到此处</p>
            <p style="color: #666; font-size: 14px;">支持 JPG、PNG、BMP、GIF 格式</p>
            <input type="file" id="fileInput" accept="image/*">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择图片
            </button>
        </div>

        <div id="loading" class="loading" style="display: none;">
            🔄 正在识别中，请稍候...
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="resultContainer" class="result-container">
            <div class="result-header">
                <h3>📊 识别结果</h3>
                <div id="resultSummary"></div>
            </div>

            <div class="text-list">
                <h4>📝 识别到的文字</h4>
                <div id="textList"></div>
            </div>

            <div>
                <h4>📋 详细信息</h4>
                <table class="details-table" id="detailsTable">
                    <thead>
                        <tr>
                            <th>文字内容</th>
                            <th>置信度</th>
                            <th>位置(X,Y)</th>
                            <th>尺寸(W×H)</th>
                        </tr>
                    </thead>
                    <tbody id="detailsBody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const resultContainer = document.getElementById('resultContainer');

        // 文件拖拽处理
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 文件选择处理
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 处理文件上传和OCR识别
        async function handleFile(file) {
            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showError('请选择图片文件');
                return;
            }

            // 显示加载状态
            hideError();
            hideResult();
            showLoading();

            try {
                // 创建FormData
                const formData = new FormData();
                formData.append('file', file);

                // 发送OCR请求
                const response = await fetch('/ocr', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(result);
                } else {
                    const errorText = await response.text();
                    showError(`识别失败: ${errorText}`);
                }
            } catch (err) {
                showError(`请求失败: ${err.message}`);
            } finally {
                hideLoading();
            }
        }

        // 显示识别结果
        function showResult(result) {
            const { ocr_result, image_info, filename, processing_time } = result;
            
            // 显示摘要信息
            document.getElementById('resultSummary').innerHTML = `
                <p><strong>文件名:</strong> ${filename}</p>
                <p><strong>图片尺寸:</strong> ${image_info.width} × ${image_info.height}</p>
                <p><strong>识别文字数:</strong> ${ocr_result.total_count}</p>
                <p><strong>处理时间:</strong> ${new Date(processing_time).toLocaleString()}</p>
            `;

            // 显示文字列表
            const textListHtml = ocr_result.texts.map(text => 
                `<div class="text-item">${text}</div>`
            ).join('');
            document.getElementById('textList').innerHTML = textListHtml || '<p>未识别到文字</p>';

            // 显示详细信息表格
            const detailsHtml = ocr_result.details.map(detail => `
                <tr>
                    <td>${detail.text}</td>
                    <td>${(detail.confidence * 100).toFixed(1)}%</td>
                    <td>${detail.position.x}, ${detail.position.y}</td>
                    <td>${detail.position.width} × ${detail.position.height}</td>
                </tr>
            `).join('');
            document.getElementById('detailsBody').innerHTML = detailsHtml;

            resultContainer.style.display = 'block';
        }

        // 显示/隐藏状态
        function showLoading() {
            loading.style.display = 'block';
        }

        function hideLoading() {
            loading.style.display = 'none';
        }

        function showError(message) {
            error.textContent = message;
            error.style.display = 'block';
        }

        function hideError() {
            error.style.display = 'none';
        }

        function hideResult() {
            resultContainer.style.display = 'none';
        }
    </script>
</body>
</html>
