#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 duration_check 功能的脚本
"""

from execute_engine_rules import execute_engine_rules


def test_duration_check():
    """测试视频时长检查功能"""
    
    # 测试规则 - 视频时长限制
    rule = {
        "rule_id": 4,
        "rule_name": "视频时长限制",
        "business_category": "通用业务（适用所有金融产品广告）",
        "rule_category": "素材制作",
        "rule_description": "广告视频长度不得超过规定时长（如60秒）",
        "priority": "MEDIUM",
        "audit_type": "ENGINE",
        "status": "TRUE",
        "engine_type": "duration_check",
        "engine_config": {
            "text_sources": "all",
            "threshold": {
                "max_duration": 60
            }
        },
        "engine_rule": {
            "logic": "AND",
            "conditions": [
                {
                    "type": "duration_limit",
                    "max_seconds": 60
                }
            ]
        },
        "llm_config": "",
        "llm_rule": "",
        "violation_template": "违规：视频长度超出规定范围，审核不通过。"
    }
    
    # 测试数据1：时长符合要求的视频（56.82秒 < 60秒）
    content_data_pass = {
        "file_info": {
            "filename": "众安贷视频.mp4",
            "content_type": "video/mp4",
            "file_size": 48845744,
            "file_path": "/var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/tmpvdqitmx8.mp4",
            "duration": 56.82,
            "fps": 25
        },
        "analysis_result": {
            "ocr_result": ["测试OCR文本"],
            "asr_result": ["测试ASR文本"]
        }
    }
    
    # 测试数据2：时长超出要求的视频（65秒 > 60秒）
    content_data_fail = {
        "file_info": {
            "filename": "超长视频.mp4",
            "content_type": "video/mp4",
            "file_size": 58845744,
            "file_path": "/var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/tmplong.mp4",
            "duration": 65.0,
            "fps": 25
        },
        "analysis_result": {
            "ocr_result": ["测试OCR文本"],
            "asr_result": ["测试ASR文本"]
        }
    }
    
    # 测试数据3：没有时长信息的数据
    content_data_no_duration = {
        "file_info": {
            "filename": "无时长信息.mp4",
            "content_type": "video/mp4",
            "file_size": 38845744,
            "file_path": "/var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/tmpnodur.mp4",
            "fps": 25
            # 注意：没有 duration 字段
        },
        "analysis_result": {
            "ocr_result": ["测试OCR文本"],
            "asr_result": ["测试ASR文本"]
        }
    }
    
    print("=" * 60)
    print("测试 duration_check 功能")
    print("=" * 60)
    
    # 测试1：时长符合要求
    print("\n🧪 测试1：时长符合要求的视频（56.82秒 < 60秒）")
    result1 = execute_engine_rules(content_data_pass, rule)
    print(f"结果: {result1}")
    rule_result1 = result1['rule_results'][0]
    print(f"是否违规: {rule_result1['is_violation']}")
    print(f"违规原因: {rule_result1['reason']}")
    assert not rule_result1['is_violation'], "时长符合要求的视频不应该被标记为违规"
    print("✅ 测试1通过")
    
    # 测试2：时长超出要求
    print("\n🧪 测试2：时长超出要求的视频（65秒 > 60秒）")
    result2 = execute_engine_rules(content_data_fail, rule)
    print(f"结果: {result2}")
    rule_result2 = result2['rule_results'][0]
    print(f"是否违规: {rule_result2['is_violation']}")
    print(f"违规原因: {rule_result2['reason']}")
    assert rule_result2['is_violation'], "时长超出要求的视频应该被标记为违规"
    print("✅ 测试2通过")
    
    # 测试3：没有时长信息
    print("\n🧪 测试3：没有时长信息的数据")
    result3 = execute_engine_rules(content_data_no_duration, rule)
    print(f"结果: {result3}")
    rule_result3 = result3['rule_results'][0]
    print(f"是否违规: {rule_result3['is_violation']}")
    print(f"违规原因: {rule_result3['reason']}")
    assert not rule_result3['is_violation'], "没有时长信息的数据不应该被标记为违规"
    print("✅ 测试3通过")
    
    print("\n" + "=" * 60)
    print("✅ 所有 duration_check 测试通过！")
    print("=" * 60)


def test_duration_check_with_min_max():
    """测试同时包含最小和最大时长限制的规则"""
    
    # 测试规则 - 视频时长限制（最小30秒，最大60秒）
    rule_min_max = {
        "rule_id": 5,
        "rule_name": "视频时长范围限制",
        "business_category": "通用业务（适用所有金融产品广告）",
        "rule_category": "素材制作",
        "rule_description": "广告视频长度必须在30-60秒之间",
        "priority": "MEDIUM",
        "audit_type": "ENGINE",
        "status": "TRUE",
        "engine_type": "duration_check",
        "engine_config": {
            "text_sources": "all",
            "threshold": {
                "min_duration": 30,
                "max_duration": 60
            }
        },
        "engine_rule": {
            "logic": "AND",
            "conditions": [
                {
                    "type": "duration_limit",
                    "min_seconds": 30,
                    "max_seconds": 60
                }
            ]
        },
        "llm_config": "",
        "llm_rule": "",
        "violation_template": "违规：视频长度不在规定范围内，审核不通过。"
    }
    
    # 测试数据：时长过短（20秒 < 30秒）
    content_data_too_short = {
        "file_info": {
            "filename": "短视频.mp4",
            "content_type": "video/mp4",
            "file_size": 18845744,
            "file_path": "/var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/tmpshort.mp4",
            "duration": 20.0,
            "fps": 25
        },
        "analysis_result": {
            "ocr_result": ["测试OCR文本"],
            "asr_result": ["测试ASR文本"]
        }
    }
    
    # 测试数据：时长合适（45秒，在30-60秒之间）
    content_data_good = {
        "file_info": {
            "filename": "合适视频.mp4",
            "content_type": "video/mp4",
            "file_size": 38845744,
            "file_path": "/var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/tmpgood.mp4",
            "duration": 45.0,
            "fps": 25
        },
        "analysis_result": {
            "ocr_result": ["测试OCR文本"],
            "asr_result": ["测试ASR文本"]
        }
    }
    
    # 测试数据：时长过长（70秒 > 60秒）
    content_data_too_long = {
        "file_info": {
            "filename": "长视频.mp4",
            "content_type": "video/mp4",
            "file_size": 68845744,
            "file_path": "/var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/tmplong.mp4",
            "duration": 70.0,
            "fps": 25
        },
        "analysis_result": {
            "ocr_result": ["测试OCR文本"],
            "asr_result": ["测试ASR文本"]
        }
    }
    
    print("\n" + "=" * 60)
    print("测试 duration_check 功能（最小和最大时长限制）")
    print("=" * 60)
    
    # 测试1：时长过短
    print("\n🧪 测试1：时长过短的视频（20秒 < 30秒）")
    result1 = execute_engine_rules(content_data_too_short, rule_min_max)
    rule_result1 = result1['rule_results'][0]
    print(f"是否违规: {rule_result1['is_violation']}")
    print(f"违规原因: {rule_result1['reason']}")
    assert rule_result1['is_violation'], "时长过短的视频应该被标记为违规"
    print("✅ 测试1通过")
    
    # 测试2：时长合适
    print("\n🧪 测试2：时长合适的视频（45秒，在30-60秒之间）")
    result2 = execute_engine_rules(content_data_good, rule_min_max)
    rule_result2 = result2['rule_results'][0]
    print(f"是否违规: {rule_result2['is_violation']}")
    print(f"违规原因: {rule_result2['reason']}")
    assert not rule_result2['is_violation'], "时长合适的视频不应该被标记为违规"
    print("✅ 测试2通过")
    
    # 测试3：时长过长
    print("\n🧪 测试3：时长过长的视频（70秒 > 60秒）")
    result3 = execute_engine_rules(content_data_too_long, rule_min_max)
    rule_result3 = result3['rule_results'][0]
    print(f"是否违规: {rule_result3['is_violation']}")
    print(f"违规原因: {rule_result3['reason']}")
    assert rule_result3['is_violation'], "时长过长的视频应该被标记为违规"
    print("✅ 测试3通过")
    
    print("\n" + "=" * 60)
    print("✅ 所有最小/最大时长限制测试通过！")
    print("=" * 60)


if __name__ == "__main__":
    # 运行基础测试
    test_duration_check()
    
    # 运行最小/最大时长限制测试
    test_duration_check_with_min_max()
    
    print("\n🎉 所有测试完成！duration_check 功能工作正常。")
