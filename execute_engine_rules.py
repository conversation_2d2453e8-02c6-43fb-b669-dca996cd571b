import json
import re


def execute_engine_rules(content_data, rules):
    """工程审核规则执行引擎 - 支持规则列表"""
    
    def extract_text_content(content_data, text_sources):
        """提取文本内容"""
        text_parts = []
        
        if 'ocr' in text_sources or 'all' in text_sources:
            # 修正字段名：使用 ocr_result 而不是 ocr_results
            ocr_results = content_data.get('analysis_result', {}).get('ocr_result', [])
            text_parts.extend(ocr_results)
        
        if 'asr' in text_sources or 'all' in text_sources:
            # 修正字段名：使用 asr_result 而不是 asr_results
            asr_results = content_data.get('analysis_result', {}).get('asr_result', [])
            text_parts.extend(asr_results)
        
        return ' '.join(text_parts)
    
    def create_result(passed, reason, confidence, details=None):
        """创建标准化结果"""
        result = {
            'passed': passed,
            'reason': reason,
            'confidence': confidence,
            'details': details or {}
        }
        return result
    
    def replace_keywords_in_template(template, found_keywords):
        """替换模板中的关键词占位符"""
        if not template or not found_keywords:
            return template
        
        # 替换 ${KEYWORDS} 占位符
        keywords_str = ', '.join(found_keywords)
        template = template.replace('${KEYWORDS}', keywords_str)
        template = template.replace('#{keywords}', keywords_str)
        
        return template
    
    def execute_single_rule(content_data, rule):
        """执行单个规则"""
        
        def check_duration_limit(content_data, engine_rule, engine_config):
            """检查视频时长限制"""
            # 从 file_info 中获取视频时长
            file_info = content_data.get('file_info', {})
            duration = file_info.get('duration')
            
            if duration is None:
                return create_result(True, 'no_duration_info', 1.0,
                                   {'message': '无法获取视频时长信息'})
            
            conditions = engine_rule.get('conditions', [])
            found_violations = []
            
            for condition in conditions:
                condition_type = condition.get('type')
                
                if condition_type == 'duration_limit':
                    max_seconds = condition.get('max_seconds')
                    min_seconds = condition.get('min_seconds')
                    
                    # 检查最大时长限制
                    if max_seconds is not None and duration > max_seconds:
                        found_violations.append(f'视频时长{duration:.2f}秒超过最大限制{max_seconds}秒')
                    
                    # 检查最小时长限制
                    if min_seconds is not None and duration < min_seconds:
                        found_violations.append(f'视频时长{duration:.2f}秒低于最小限制{min_seconds}秒')
            
            if found_violations:
                return create_result(False, 'duration_limit_violated', 1.0, 
                                   {'violations': found_violations, 'actual_duration': duration})
            else:
                return create_result(True, 'duration_within_limits', 1.0, 
                                   {'actual_duration': duration})
        
        def check_complex_match(text, engine_rule, engine_config):
            """复杂匹配检查 - 支持多条件组合"""
            conditions = engine_rule.get('conditions', [])
            logic = engine_rule.get('logic', 'AND')
            case_sensitive = engine_config.get('case_sensitive', False)
            
            if not case_sensitive:
                text = text.lower()
            
            condition_results = []
            found_keywords = []
            
            for condition in conditions:
                condition_type = condition.get('type')
                condition_result = False
                
                if condition_type == 'contains':
                    keywords = condition.get('keywords', [])
                    if not case_sensitive:
                        keywords = [k.lower() for k in keywords]
                    
                    condition_logic = condition.get('logic', 'OR')
                    if condition_logic == 'OR':
                        for keyword in keywords:
                            if keyword in text:
                                condition_result = True
                                found_keywords.append(keyword)
                                break
                    else:  # AND
                        condition_result = True
                        for keyword in keywords:
                            if keyword not in text:
                                condition_result = False
                                break
                            else:
                                found_keywords.append(keyword)
                
                elif condition_type == 'regex_match':
                    patterns = condition.get('patterns', [])
                    for pattern in patterns:
                        if re.search(pattern, text):
                            condition_result = True
                            found_keywords.append(pattern)
                            break
                
                elif condition_type == 'conditional_cross_source':
                    if_ocr_contains = condition.get('if_ocr_contains', [])
                    then_asr_must_contain = condition.get('then_asr_must_contain', [])
                    
                    ocr_text = ' '.join(content_data.get('analysis_result', {}).get('ocr_result', []))
                    if not case_sensitive:
                        ocr_text = ocr_text.lower()
                        if_ocr_contains = [k.lower() for k in if_ocr_contains]
                    
                    ocr_condition_met = False
                    for keyword in if_ocr_contains:
                        if keyword in ocr_text:
                            ocr_condition_met = True
                            break
                    
                    if ocr_condition_met:
                        asr_text = ' '.join(content_data.get('analysis_result', {}).get('asr_result', []))
                        if not case_sensitive:
                            asr_text = asr_text.lower()
                            then_asr_must_contain = [k.lower() for k in then_asr_must_contain]
                        
                        for required in then_asr_must_contain:
                            if required in asr_text:
                                condition_result = True
                                found_keywords.append(required)
                                break
                    else:
                        condition_result = True
                
                condition_results.append(condition_result)
            
            if logic == 'AND':
                final_result = all(condition_results)
            else:  # OR
                final_result = any(condition_results)
            
            if final_result:
                return create_result(False, 'rule_violation_detected', 1.0, {'found_keywords': found_keywords})
            else:
                return create_result(True, 'no_violation_found', 1.0)
        
        def check_contains_all(text, engine_rule, engine_config):
            """检查是否包含所有关键词"""
            keywords = engine_rule.get('keywords', [])
            case_sensitive = engine_config.get('case_sensitive', False)
            
            if not case_sensitive:
                text = text.lower()
                keywords = [k.lower() for k in keywords]
            
            missing_keywords = []
            found_keywords = []
            for keyword in keywords:
                if keyword not in text:
                    missing_keywords.append(keyword)
                else:
                    found_keywords.append(keyword)
            
            if len(missing_keywords) == 0:
                return create_result(True, 'all_keywords_found', 1.0, {'found_keywords': found_keywords})
            else:
                return create_result(False, 'missing_keywords', 0.8, 
                                   {'missing': missing_keywords, 'found_keywords': found_keywords})
        
        def check_regex_match(text, engine_rule, engine_config):
            """正则表达式匹配检查"""
            patterns = engine_rule.get('patterns', [])
            
            matched_patterns = []
            for pattern in patterns:
                if re.search(pattern, text):
                    matched_patterns.append(pattern)
            
            if len(matched_patterns) > 0:
                return create_result(False, 'regex_pattern_matched', 1.0,
                                   {'matched_patterns': matched_patterns, 'found_keywords': matched_patterns})
            else:
                return create_result(True, 'no_pattern_matched', 1.0)
        
        def check_keyword_logic(text, engine_rule, engine_config):
            """关键词逻辑检查"""
            # 对于 keyword_logic 类型，需要从 conditions 中提取关键词
            conditions = engine_rule.get('conditions', [])
            logic = engine_rule.get('logic', 'OR')
            case_sensitive = engine_config.get('case_sensitive', False)

            if not case_sensitive:
                text = text.lower()

            found_keywords = []
            all_keywords = []

            # 从 conditions 中提取关键词
            for condition in conditions:
                if condition.get('type') == 'contains':
                    keywords = condition.get('keywords', [])
                    all_keywords.extend(keywords)

                    if not case_sensitive:
                        keywords = [k.lower() for k in keywords]

                    for keyword in keywords:
                        if keyword in text:
                            found_keywords.append(keyword)

            # 如果没有从 conditions 中找到关键词，尝试直接从 engine_rule 中获取
            if not all_keywords:
                keywords = engine_rule.get('keywords', [])
                all_keywords = keywords
                if not case_sensitive:
                    keywords = [k.lower() for k in keywords]

                for keyword in keywords:
                    if keyword in text:
                        found_keywords.append(keyword)

            if logic == 'AND':
                if len(found_keywords) == len(all_keywords):
                    return create_result(False, 'all_forbidden_keywords_found', 1.0,
                                       {'found_keywords': found_keywords})
                else:
                    return create_result(True, 'not_all_keywords_found', 1.0)
            else:  # OR
                if len(found_keywords) > 0:
                    return create_result(False, 'forbidden_keywords_found', 1.0,
                                       {'found_keywords': found_keywords})
                else:
                    return create_result(True, 'no_forbidden_keywords', 1.0)
        
        # 解析 JSON 字符串配置
        try:
            if isinstance(rule.get('engine_config'), str):
                engine_config = json.loads(rule.get('engine_config', '{}'))
            else:
                engine_config = rule.get('engine_config', {})
        except json.JSONDecodeError:
            engine_config = {}
        
        try:
            if isinstance(rule.get('engine_rule'), str):
                engine_rule = json.loads(rule.get('engine_rule', '{}'))
            else:
                engine_rule = rule.get('engine_rule', {})
        except json.JSONDecodeError:
            engine_rule = {}
        
        # 单个规则执行逻辑
        engine_type = rule.get('engine_type')
        
        # 对于 duration_check 类型，不需要提取文本内容
        if engine_type == 'duration_check':
            result = check_duration_limit(content_data, engine_rule, engine_config)
        else:
            # 其他类型需要提取文本内容
            text_sources = engine_config.get('text_sources', ['all'])
            if isinstance(text_sources, str):
                text_sources = [text_sources]
            
            text_content = extract_text_content(content_data, text_sources)
            
            if engine_type == 'complex_match':
                result = check_complex_match(text_content, engine_rule, engine_config)
            elif engine_type == 'contains_all':
                result = check_contains_all(text_content, engine_rule, engine_config)
            elif engine_type == 'regex_match':
                result = check_regex_match(text_content, engine_rule, engine_config)
            elif engine_type == 'keyword_logic':
                result = check_keyword_logic(text_content, engine_rule, engine_config)
            else:
                result = create_result(False, 'unknown_engine_type', 0.0,
                                     {'engine_type': engine_type})
        
        threshold = engine_config.get('threshold', {}).get('confidence', 0.8)
        if result['confidence'] < threshold:
            result.update({'passed': False})
            result.update({'reason': 'confidence_below_threshold'})
        
        # 构建最终的规则结果
        found_keywords = result.get('details', {}).get('found_keywords', [])
        violation_template = rule.get('violation_template', '')

        # 只有在规则检查失败时才应用违规模板
        if not result['passed']:
            reason = replace_keywords_in_template(violation_template, found_keywords)
        else:
            reason = ''

        rule_result = {
            'rule_id': rule.get('rule_id', rule.get('编号', 'unknown')),
            'rule_name': rule.get('rule_name', 'unknown'),
            'rule_description': rule.get('rule_description', 'unknown'),
            'rule_info': rule.get('engine_rule', {}),
            'priority': rule.get('priority', 'MEDIUM'),
            'is_violation': not result['passed'],
            'reason': reason
        }
        
        return rule_result
    
    # 主执行逻辑 - 处理规则列表
    if not isinstance(rules, list):
        # 如果传入的是单个规则，转换为列表
        rules = [rules]
    
    rule_results = []
    
    # 执行所有规则
    for rule in rules:
        # 修复状态检查逻辑：支持布尔值和字符串
        status = rule.get('status')
        if status is False or status == 'INACTIVE':
            continue
        
        rule_result = execute_single_rule(content_data, rule)
        rule_results.append(rule_result)
    
    # 返回简化的结果
    return {
        'rule_results': rule_results
    }
