#!/bin/bash

# 视频分析服务一键启动脚本 - 服务器版
# 基于本地start_services.sh修改，适配服务器环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径 - 服务器环境
PROJECT_DIR="/alidata1/admin/material-review"
ASR_DIR="$PROJECT_DIR/za-video-asr-master"
VIDEO_DIR="$PROJECT_DIR/data-video-recog-master"

# 日志文件
LOG_DIR="$PROJECT_DIR/logs"
ASR_LOG="$LOG_DIR/asr_service.log"
VIDEO_LOG="$LOG_DIR/video_service.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

echo -e "${BLUE}🚀 视频分析服务启动脚本 - 服务器版${NC}"
echo -e "${BLUE}================================${NC}"

# 检查Python环境
check_python() {
    # 服务器使用conda环境
    if command -v conda &> /dev/null; then
        echo -e "${GREEN}✅ Conda环境检查通过${NC}"
        # 激活py39环境
        source $(conda info --base)/etc/profile.d/conda.sh
        conda activate py39 2>/dev/null || echo "警告: 无法激活py39环境"
        echo "当前Python: $(which python)"
    elif command -v python3 &> /dev/null; then
        echo -e "${GREEN}✅ Python3 环境检查通过${NC}"
    else
        echo -e "${RED}❌ Python 未找到${NC}"
        exit 1
    fi
}

# 检查项目目录
check_directories() {
    if [ ! -d "$ASR_DIR" ]; then
        echo -e "${RED}❌ ASR项目目录不存在: $ASR_DIR${NC}"
        exit 1
    fi
    
    if [ ! -d "$VIDEO_DIR" ]; then
        echo -e "${RED}❌ 视频分析项目目录不存在: $VIDEO_DIR${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 项目目录检查通过${NC}"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    
    if netstat -tlnp | grep ":$port " >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $port 已被占用，正在尝试停止现有服务...${NC}"
        # 杀死占用端口的进程
        pkill -f "python.*$port" 2>/dev/null
        sleep 2
        
        if netstat -tlnp | grep ":$port " >/dev/null 2>&1; then
            echo -e "${RED}❌ 无法释放端口 $port，请手动检查${NC}"
            exit 1
        fi
    fi
    echo -e "${GREEN}✅ 端口 $port ($service_name) 可用${NC}"
}

# 启动ASR服务
start_asr_service() {
    echo -e "${YELLOW}🎤 启动ASR服务 (端口8080)...${NC}"
    
    cd "$ASR_DIR"
    
    # 检查ASR启动方式
    if [ -f "app/server.py" ]; then
        nohup python -m app.server > "$ASR_LOG" 2>&1 &
    elif [ -f "run.sh" ]; then
        nohup bash run.sh > "$ASR_LOG" 2>&1 &
    else
        echo -e "${RED}❌ ASR启动文件未找到${NC}"
        return 1
    fi
    
    ASR_PID=$!
    echo "$ASR_PID" > "$LOG_DIR/asr.pid"
    echo -e "${BLUE}   ASR服务PID: $ASR_PID${NC}"
    
    # 等待服务启动 - ASR服务需要更长时间加载模型
    echo -e "${YELLOW}   等待ASR服务启动（模型加载需要1-2分钟）...${NC}"
    for i in {1..60}; do
        # 检查健康状态，ASR服务返回 "ok" 表示成功
        health_response=$(curl -s http://localhost:8080/health 2>/dev/null)
        if [ "$health_response" = '"ok"' ]; then
            echo -e "${GREEN}✅ ASR服务启动成功 (http://localhost:8080)${NC}"
            return 0
        fi
        sleep 2
        echo -n "."
    done
    
    echo -e "${RED}❌ ASR服务启动失败，请检查日志: $ASR_LOG${NC}"
    return 1
}

# 启动视频分析服务 - 使用real_video_server.py
start_video_service() {
    echo -e "${YELLOW}🎬 启动视频分析服务 (端口8081)...${NC}"
    
    cd "$VIDEO_DIR"
    
    # 安装必要的依赖
    echo -e "${YELLOW}   检查并安装视频分析依赖...${NC}"

    # 检查关键依赖是否已安装
    python -c "import docx" 2>/dev/null || pip install python-docx==1.1.0
    python -c "import moviepy" 2>/dev/null || pip install moviepy
    python -c "import cv2" 2>/dev/null || pip install opencv-python
    python -c "import rapidocr_onnxruntime" 2>/dev/null || pip install rapidocr-onnxruntime
    python -c "import fastapi" 2>/dev/null || pip install fastapi
    python -c "import uvicorn" 2>/dev/null || pip install uvicorn
    python -c "import multipart" 2>/dev/null || pip install python-multipart

    echo -e "${GREEN}   ✅ 依赖检查完成${NC}"
    
    # 启动main_server.py（原real_video_server.py）
    nohup python server/main_server.py > "$VIDEO_LOG" 2>&1 &
    VIDEO_PID=$!
    
    echo "$VIDEO_PID" > "$LOG_DIR/video.pid"
    echo -e "${BLUE}   视频分析服务PID: $VIDEO_PID${NC}"
    
    # 等待服务启动
    echo -e "${YELLOW}   等待视频分析服务启动...${NC}"
    for i in {1..30}; do
        # 尝试多种健康检查方式
        if curl -s http://localhost:8081/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 视频分析服务启动成功 (http://localhost:8081)${NC}"
            return 0
        elif curl -s http://localhost:8081/ >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 视频分析服务启动成功 (http://localhost:8081)${NC}"
            return 0
        fi
        sleep 2
        echo -n "."
    done
    
    echo -e "${RED}❌ 视频分析服务启动失败，请检查日志: $VIDEO_LOG${NC}"
    return 1
}

# 显示服务状态
show_status() {
    echo -e "\n${BLUE}📊 服务状态${NC}"
    echo -e "${BLUE}================================${NC}"
    
    # ASR服务状态
    if curl -s http://localhost:8080/health >/dev/null 2>&1; then
        echo -e "${GREEN}🎤 ASR服务: 运行中 (http://localhost:8080)${NC}"
    else
        echo -e "${RED}🎤 ASR服务: 未运行${NC}"
    fi
    
    # 视频分析服务状态
    if curl -s http://localhost:8081/health >/dev/null 2>&1; then
        echo -e "${GREEN}🎬 视频分析服务: 运行中 (http://localhost:8081)${NC}"
        echo -e "${GREEN}   📝 Demo页面: http://localhost:8081/demo${NC}"
        echo -e "${GREEN}   📚 API文档: http://localhost:8081/docs${NC}"
        
        # 显示服务能力
        echo -e "${YELLOW}   🔍 服务能力检查...${NC}"
        curl -s http://localhost:8081/ | python -m json.tool 2>/dev/null || echo "   服务响应正常"
    else
        echo -e "${RED}🎬 视频分析服务: 未运行${NC}"
    fi
    
    echo -e "\n${BLUE}📋 日志文件${NC}"
    echo -e "${BLUE}================================${NC}"
    echo -e "ASR服务日志: $ASR_LOG"
    echo -e "视频服务日志: $VIDEO_LOG"
    
    echo -e "\n${BLUE}🔧 管理命令${NC}"
    echo -e "${BLUE}================================${NC}"
    echo -e "停止服务: ./stop_services.sh"
    echo -e "查看ASR日志: tail -f $ASR_LOG"
    echo -e "查看视频日志: tail -f $VIDEO_LOG"
    echo -e "测试上传: curl -X POST -F 'file=@test.mp4' http://localhost:8081/upload"
}

# 主函数
main() {
    echo -e "${YELLOW}开始启动服务...${NC}\n"
    
    # 检查环境
    check_python
    check_directories
    
    # 检查端口
    check_port 8080 "ASR服务"
    check_port 8081 "视频分析服务"
    
    echo ""
    
    # 启动服务
    if start_asr_service; then
        sleep 3
        if start_video_service; then
            echo -e "\n${GREEN}🎉 所有服务启动成功！${NC}"
            show_status
        else
            echo -e "\n${RED}❌ 视频分析服务启动失败${NC}"
            exit 1
        fi
    else
        echo -e "\n${RED}❌ ASR服务启动失败${NC}"
        exit 1
    fi
}

# 运行主函数
main
